#!/usr/bin/env node

const https = require('https');
const http = require('http');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testEndpoint(url, name) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          log(`✅ ${name}: OK (${res.statusCode})`, 'green');
          resolve({ success: true, status: res.statusCode, data });
        } else {
          log(`⚠️  ${name}: Warning (${res.statusCode})`, 'yellow');
          resolve({ success: false, status: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      log(`❌ ${name}: Failed - ${error.message}`, 'red');
      resolve({ success: false, error: error.message });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      log(`⏰ ${name}: Timeout`, 'yellow');
      resolve({ success: false, error: 'Timeout' });
    });
  });
}

async function testEnvironments() {
  log('🧪 Testing Environment Endpoints', 'cyan');
  log('================================', 'cyan');

  const endpoints = [
    {
      url: 'https://gudangmitra-production.up.railway.app/health',
      name: 'Production Backend Health'
    },
    {
      url: 'https://gudangmitra-production.up.railway.app/api/test-connection',
      name: 'Production Database Connection'
    },
    {
      url: 'https://gudangmitra-staging.up.railway.app/health',
      name: 'Staging Backend Health'
    },
    {
      url: 'https://gudangmitra-staging.up.railway.app/api/test-connection',
      name: 'Staging Database Connection'
    },
    {
      url: 'https://gudang.netlify.app',
      name: 'Production Frontend'
    }
  ];

  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint.url, endpoint.name);
    results.push({ ...endpoint, ...result });
  }

  log('\n📊 Test Summary:', 'cyan');
  log('================', 'cyan');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  log(`✅ Successful: ${successful}/${total}`, successful === total ? 'green' : 'yellow');
  
  if (successful < total) {
    log('\n⚠️  Failed endpoints:', 'yellow');
    results.filter(r => !r.success).forEach(r => {
      log(`   - ${r.name}: ${r.error || `HTTP ${r.status}`}`, 'red');
    });
  }

  log('\n💡 Tips:', 'blue');
  log('- Make sure Railway services are deployed and running', 'blue');
  log('- Check environment variables are set correctly', 'blue');
  log('- Verify database connections in Railway dashboard', 'blue');
  
  return results;
}

// Run tests if called directly
if (require.main === module) {
  testEnvironments().catch(console.error);
}

module.exports = { testEnvironments, testEndpoint };
