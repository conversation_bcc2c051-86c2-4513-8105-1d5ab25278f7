#!/usr/bin/env node

const { execSync } = require('child_process');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('📦 Git Repository Setup Guide', 'cyan');
  log('=============================', 'cyan');

  log('\n🎯 You have 2 options to set up Git repository:', 'yellow');

  log('\n📋 OPTION 1: Create New GitHub Repository (Recommended)', 'cyan');
  log('=====================================================', 'cyan');
  
  log('\n🌐 1.1 Create Repository:', 'yellow');
  log('1. Go to: https://github.com/new', 'blue');
  log('2. Repository name: gudangsub-staging', 'blue');
  log('3. Description: Gudang Mitra - Staging Environment', 'blue');
  log('4. Set to Public or Private (your choice)', 'blue');
  log('5. DO NOT initialize with README (we already have files)', 'blue');
  log('6. Click "Create repository"', 'blue');

  log('\n🔗 1.2 Connect Local Repository:', 'yellow');
  log('After creating the repository, GitHub will show you commands like:', 'blue');
  log('', 'blue');
  log('git remote add origin https://github.com/[username]/gudangsub-staging.git', 'blue');
  log('git branch -M main', 'blue');
  log('git push -u origin main', 'blue');
  log('', 'blue');
  log('Then push staging branch:', 'blue');
  log('git push -u origin staging', 'blue');

  log('\n📋 OPTION 2: Use Existing Repository', 'cyan');
  log('====================================', 'cyan');
  
  log('\n🔄 2.1 If you already have a repository for this project:', 'yellow');
  log('1. Get your repository URL (e.g., from GitHub)', 'blue');
  log('2. Add it as remote:', 'blue');
  log('   git remote add origin [your-repository-url]', 'blue');
  log('3. Push main branch first:', 'blue');
  log('   git push -u origin main', 'blue');
  log('4. Push staging branch:', 'blue');
  log('   git push -u origin staging', 'blue');

  log('\n⚡ Quick Commands Template:', 'cyan');
  log('===========================', 'cyan');
  
  log('\n📝 Replace [username] and [repo-name] with your actual values:', 'yellow');
  log('', 'blue');
  log('# Add remote (choose one):', 'blue');
  log('git remote add origin https://github.com/[username]/[repo-name].git', 'blue');
  log('# or', 'blue');
  log('git remote add origin https://github.com/[username]/gudangsub-staging.git', 'blue');
  log('', 'blue');
  log('# Push main branch first:', 'blue');
  log('git checkout main', 'blue');
  log('git push -u origin main', 'blue');
  log('', 'blue');
  log('# Push staging branch:', 'blue');
  log('git checkout staging', 'blue');
  log('git push -u origin staging', 'blue');

  log('\n🔍 Verify Setup:', 'cyan');
  log('================', 'cyan');
  
  log('\n✅ After setting up the repository:', 'yellow');
  log('1. Check remotes: git remote -v', 'blue');
  log('2. Check branches: git branch -a', 'blue');
  log('3. Verify both branches are pushed to GitHub', 'blue');

  log('\n📋 Next Steps After Git Setup:', 'cyan');
  log('==============================', 'cyan');
  
  log('\n🌐 1. Setup Netlify Branch Deploy:', 'yellow');
  log('   - Go to https://app.netlify.com/', 'blue');
  log('   - Connect your GitHub repository', 'blue');
  log('   - Enable branch deploys for "staging"', 'blue');
  log('', 'blue');
  log('🗄️  2. Complete Railway Database Setup:', 'yellow');
  log('   - Follow the manual-setup-guide.cjs instructions', 'blue');
  log('   - Set database variables in Railway dashboard', 'blue');
  log('', 'blue');
  log('🔧 3. Update Environment URLs:', 'yellow');
  log('   - Get backend URL from Railway', 'blue');
  log('   - Get frontend URL from Netlify', 'blue');
  log('   - Update CORS configuration', 'blue');

  log('\n🆘 Need Help?', 'cyan');
  log('=============', 'cyan');
  
  log('\n💡 Common Issues:', 'yellow');
  log('❌ "Permission denied" error:', 'blue');
  log('   → Set up SSH keys or use HTTPS with token', 'blue');
  log('', 'blue');
  log('❌ "Repository not found" error:', 'blue');
  log('   → Check repository URL and permissions', 'blue');
  log('', 'blue');
  log('❌ "Branch already exists" error:', 'blue');
  log('   → Use git push origin staging (without -u)', 'blue');

  log('\n✅ Ready to set up Git repository!', 'green');
  log('Choose Option 1 (new repo) or Option 2 (existing repo) above.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
