# 🎯 FINAL SETUP STEPS - Staging Environment

## ✅ **Yang Sudah SELESAI:**
- ✅ Railway project `gudangmitra-staging` dibuat
- ✅ MySQL database staging dibuat
- ✅ Backend service `backend-staging` di-deploy
- ✅ Environment variables dasar sudah diset
- ✅ Configuration files sudah dibuat
- ✅ Scripts dan tools sudah siap
- ✅ Branch `staging` sudah dibuat dan di-commit

## 🔄 **LANGKAH TERAKHIR (Manual - 15 menit):**

### **1. Setup Database Connection (5 menit)**
```bash
# Buka Railway dashboard:
https://railway.com/project/************************************

# Langkah:
1. Klik "MySQL" service → Variables tab
2. Copy semua MYSQL_* variables
3. Klik "backend-staging" service → Variables tab  
4. Add variables:
   - DB_HOST = [MYSQL_HOST value]
   - DB_USER = [MYSQL_USER value]
   - DB_PASSWORD = [MYSQL_PASSWORD value]
   - DB_NAME = [MYSQL_DATABASE value]
   - DB_PORT = 3306
   - DB_SSL = true
```

### **2. Get Backend URL (2 menit)**
```bash
# Di Railway dashboard:
1. Klik "backend-staging" service
2. Settings tab → Networking section
3. Click "Generate Domain" 
4. Copy URL (contoh: https://backend-staging-production-xxxx.up.railway.app)

# Update .env.staging:
VITE_API_URL=https://[your-backend-url]/api
```

### **3. Push ke Git & Setup Netlify (5 menit)**
```bash
# Push staging branch:
git push origin staging

# Di Netlify dashboard (https://app.netlify.com/):
1. Select your site
2. Site Settings → Build & Deploy
3. Deploy contexts → Enable "Branch deploys"
4. Add "staging" to branch list
5. Save → Netlify akan auto-deploy staging branch
6. Copy staging URL (contoh: https://[site-id]--staging.netlify.app)
```

### **4. Update CORS (2 menit)**
```bash
# Setelah dapat Netlify staging URL:
railway variables --set "CORS_ORIGIN=https://[netlify-staging-url]"
```

### **5. Test Everything (1 menit)**
```bash
# Test backend:
curl https://[backend-staging-url]/health
curl https://[backend-staging-url]/api/test-connection

# Test frontend:
# Buka https://[netlify-staging-url] di browser

# Run test script:
node test-staging-complete.cjs
```

## 🎯 **Expected Final URLs:**

### **Production (Tidak Berubah):**
- Frontend: `https://gudang.netlify.app`
- Backend: `https://gudangmitra-production.up.railway.app`

### **Staging (Baru):**
- Frontend: `https://[site-id]--staging.netlify.app`
- Backend: `https://backend-staging-production-[random].up.railway.app`

## 🚀 **Workflow Setelah Setup:**

### **Development → Staging:**
```bash
git checkout staging
# ... make changes ...
git add . && git commit -m "feat: new feature"
git push origin staging  # Auto-deploy ke staging
```

### **Staging → Production:**
```bash
git checkout main
git merge staging
git push origin main     # Auto-deploy ke production
```

## 🔧 **Helper Scripts:**
- `node get-database-info.cjs` - Database setup guide
- `node get-backend-url.cjs` - Backend URL guide  
- `node setup-netlify-branch.cjs` - Netlify setup guide
- `node test-staging-complete.cjs` - Test everything

## 🆘 **Troubleshooting:**

### **CORS Error:**
```bash
railway variables --set "CORS_ORIGIN=https://[correct-netlify-url]"
```

### **Database Error:**
```bash
railway variables  # Check DB credentials
railway logs       # Check error logs
```

### **Build Error:**
```bash
npm run build:staging  # Test local build
```

## ✅ **Success Checklist:**
- [ ] Database credentials set in Railway
- [ ] Backend domain generated and accessible
- [ ] .env.staging updated with correct backend URL
- [ ] Staging branch pushed to Git
- [ ] Netlify branch deploys enabled
- [ ] Netlify staging URL obtained
- [ ] CORS_ORIGIN updated in Railway
- [ ] Backend health endpoint returns 200
- [ ] Frontend staging loads without errors
- [ ] API calls work from frontend to backend

## 🎉 **Setelah Selesai:**
Anda akan memiliki:
- ✅ **Production environment** yang stabil
- ✅ **Staging environment** untuk testing
- ✅ **Isolated databases** (tidak saling mengganggu)
- ✅ **Professional workflow** untuk development

---

**🚀 Total waktu: ~15 menit untuk menyelesaikan semua langkah manual!**
