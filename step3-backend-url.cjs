#!/usr/bin/env node

const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('📋 STEP 3: Get Backend URL & Update Frontend Config', 'cyan');
  log('==================================================', 'cyan');

  log('\n🎯 GOAL: Get backend staging URL and update frontend configuration', 'yellow');

  log('\n✅ Prerequisites:', 'green');
  log('- ✅ Git repository setup complete', 'blue');
  log('- ✅ Railway database configured', 'blue');
  log('- ✅ Backend service deployed', 'blue');

  log('\n🌐 3.1 Generate Backend Domain:', 'yellow');
  log('1. Open Railway dashboard:', 'blue');
  log('   https://railway.com/project/************************************', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Click "Settings" tab', 'blue');
  log('4. Scroll to "Networking" section', 'blue');
  log('5. Look for "Public Domain"', 'blue');
  log('6. If no domain exists, click "Generate Domain"', 'blue');
  log('7. Copy the generated URL', 'blue');

  log('\n📝 3.2 Expected URL Format:', 'yellow');
  log('   https://backend-staging-production-[random-string].up.railway.app', 'blue');
  log('   Example: https://backend-staging-production-a1b2c3d4.up.railway.app', 'blue');

  log('\n🧪 3.3 Test Backend URL:', 'yellow');
  log('After getting the URL, test these endpoints in browser:', 'blue');
  log('', 'blue');
  log('   🔹 Health Check:', 'blue');
  log('   https://[your-backend-url]/health', 'blue');
  log('   Should return: {"success": true, "message": "Railway server is running"}', 'blue');
  log('', 'blue');
  log('   🔹 Database Test:', 'blue');
  log('   https://[your-backend-url]/api/test-connection', 'blue');
  log('   Should return: {"success": true, "message": "Database connection successful"}', 'blue');

  log('\n📝 3.4 Update Frontend Configuration:', 'yellow');
  
  // Show current .env.staging content
  if (fs.existsSync('.env.staging')) {
    log('Current .env.staging file:', 'blue');
    const envContent = fs.readFileSync('.env.staging', 'utf8');
    console.log(envContent);
    
    log('\n🔧 Update the VITE_API_URL line:', 'yellow');
    log('Change from:', 'blue');
    log('   VITE_API_URL=https://gudangmitra-staging.up.railway.app/api', 'red');
    log('To:', 'blue');
    log('   VITE_API_URL=https://[your-actual-backend-url]/api', 'green');
    log('', 'blue');
    log('Example:', 'blue');
    log('   VITE_API_URL=https://backend-staging-production-a1b2c3d4.up.railway.app/api', 'green');
  } else {
    log('⚠️  .env.staging file not found!', 'red');
    log('Creating .env.staging template...', 'yellow');
    
    const envTemplate = `# Staging Environment Variables
VITE_API_URL=https://[your-backend-url]/api
VITE_APP_ENV=staging`;
    
    fs.writeFileSync('.env.staging', envTemplate);
    log('✅ Created .env.staging template', 'green');
  }

  log('\n💡 EXAMPLE UPDATE:', 'cyan');
  log('==================', 'cyan');

  log('\n📝 If your backend URL is:', 'yellow');
  log('   https://backend-staging-production-a1b2c3d4.up.railway.app', 'blue');

  log('\n📝 Then update .env.staging to:', 'yellow');
  log('   VITE_API_URL=https://backend-staging-production-a1b2c3d4.up.railway.app/api', 'blue');
  log('   VITE_APP_ENV=staging', 'blue');

  log('\n🔄 3.5 Commit Frontend Changes:', 'yellow');
  log('After updating .env.staging:', 'blue');
  log('   git add .env.staging', 'blue');
  log('   git commit -m "Update staging backend URL"', 'blue');
  log('   git push origin staging', 'blue');

  log('\n✅ 3.6 Verify Configuration:', 'yellow');
  log('1. Backend health endpoint works', 'blue');
  log('2. Backend database test works', 'blue');
  log('3. Frontend .env.staging updated', 'blue');
  log('4. Changes committed to staging branch', 'blue');

  log('\n🆘 TROUBLESHOOTING:', 'cyan');
  log('==================', 'cyan');

  log('\n❌ "No domain found" in Railway:', 'red');
  log('   → Click "Generate Domain" button', 'blue');
  log('   → Wait a few seconds for domain to be created', 'blue');
  log('   → Refresh the page if needed', 'blue');

  log('\n❌ Backend URL returns error:', 'red');
  log('   → Check if service is deployed successfully', 'blue');
  log('   → Verify database variables are set correctly', 'blue');
  log('   → Check deployment logs for errors', 'blue');

  log('\n❌ Health endpoint fails:', 'red');
  log('   → Service might still be deploying', 'blue');
  log('   → Check Railway service status', 'blue');
  log('   → Verify NODE_ENV=staging is set', 'blue');

  log('\n❌ Database test fails:', 'red');
  log('   → Double-check all DB_* variables', 'blue');
  log('   → Ensure MySQL service is running', 'blue');
  log('   → Redeploy backend service', 'blue');

  log('\n📋 AFTER SUCCESSFUL BACKEND URL SETUP:', 'green');
  log('======================================', 'green');

  log('\n✅ You should have:', 'yellow');
  log('   - Working backend URL', 'blue');
  log('   - Health endpoint responding', 'blue');
  log('   - Database connection working', 'blue');
  log('   - Frontend config updated', 'blue');
  log('   - Changes committed to Git', 'blue');

  log('\n🚀 NEXT STEPS:', 'cyan');
  log('==============', 'cyan');

  log('\n📋 Progress so far:', 'yellow');
  log('1. ✅ Git repository connected', 'green');
  log('2. ✅ Railway database connected', 'green');
  log('3. ✅ Backend URL obtained and tested', 'green');
  log('4. 🔄 Setup Netlify branch deploy', 'blue');
  log('5. 🔄 Configure CORS', 'blue');
  log('6. 🔄 Test complete staging environment', 'blue');

  log('\n📝 Continue to next step:', 'yellow');
  log('   node step4-netlify-deploy.cjs', 'blue');
  log('   # OR follow COMPLETE-SETUP-CHECKLIST.md', 'blue');

  log('\n✅ Ready to get backend URL!', 'green');
  log('Open Railway dashboard and follow the steps above.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
