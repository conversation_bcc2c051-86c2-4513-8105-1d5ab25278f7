#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

function main() {
  log('🌐 Setting up Netlify Staging Environment', 'cyan');
  log('=========================================', 'cyan');

  // Check if we're on staging branch
  const branch = executeCommand('git branch --show-current', 'Checking current branch');
  if (!branch || !branch.includes('staging')) {
    log('⚠️  Not on staging branch. Switching to staging...', 'yellow');
    executeCommand('git checkout staging', 'Switching to staging branch');
  }

  // Build for staging
  executeCommand('npm run build:staging', 'Building for staging environment');

  log('\n📋 Netlify Staging Setup Instructions:', 'cyan');
  log('=====================================', 'cyan');

  log('\n🔧 Manual Setup Required in Netlify Dashboard:', 'yellow');
  log('1. Go to your Netlify dashboard', 'blue');
  log('2. Select your site (gudang.netlify.app)', 'blue');
  log('3. Go to Site Settings > Build & Deploy', 'blue');
  log('4. Enable "Branch deploys" in Deploy contexts', 'blue');
  log('5. Add "staging" to the list of branch deploy branches', 'blue');

  log('\n6. Go to Site Settings > Environment Variables', 'blue');
  log('7. Verify these variables are set:', 'blue');
  log('   - VITE_API_URL (will be set via netlify.toml)', 'blue');
  log('   - VITE_APP_ENV (will be set via netlify.toml)', 'blue');

  log('\n🚀 Deployment Steps:', 'cyan');
  log('1. Push staging branch to GitHub:', 'yellow');
  log('   git add .', 'blue');
  log('   git commit -m "Setup staging environment"', 'blue');
  log('   git push origin staging', 'blue');

  log('\n2. Netlify will automatically deploy staging branch to:', 'yellow');
  log('   https://[site-id]--staging.netlify.app', 'blue');
  log('   (or similar URL pattern)', 'blue');

  log('\n3. After deployment, update Railway backend CORS_ORIGIN:', 'yellow');
  log('   railway variables --set "CORS_ORIGIN=https://[actual-staging-url]"', 'blue');

  // Show current environment configuration
  log('\n🔧 Current Environment Configuration:', 'cyan');
  
  if (fs.existsSync('.env.staging')) {
    const envContent = fs.readFileSync('.env.staging', 'utf8');
    log('Frontend .env.staging:', 'blue');
    console.log(envContent);
  }

  if (fs.existsSync('server/.env.staging')) {
    log('\nBackend server/.env.staging exists ✅', 'green');
  } else {
    log('\nBackend server/.env.staging missing ❌', 'red');
  }

  // Show netlify.toml configuration
  if (fs.existsSync('netlify.toml')) {
    log('\nNetlify configuration (netlify.toml):', 'blue');
    const netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    console.log(netlifyConfig);
  }

  log('\n📋 Next Steps:', 'cyan');
  log('1. Complete Railway database setup (run: node setup-railway-staging.cjs)', 'yellow');
  log('2. Push staging branch to trigger Netlify deployment', 'yellow');
  log('3. Update CORS_ORIGIN in Railway with actual staging URL', 'yellow');
  log('4. Test staging environment (run: node test-environments.js)', 'yellow');

  log('\n✅ Netlify staging setup preparation completed!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { main };
