const mysql = require('mysql2/promise');

// Load environment variables dynamically based on NODE_ENV
const envFile = `.env.${process.env.NODE_ENV || 'staging'}`;
console.log(`🔧 Loading environment from: ${envFile}`);
require('dotenv').config({ path: envFile });

// Database configuration (same as railway-server.js)
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306', 10),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false
};

async function updateRequestDates() {
  let connection;
  
  try {
    console.log('🔗 Connecting to Railway database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Get current requests
    console.log('📋 Fetching current requests...');
    const [requests] = await connection.query('SELECT id, created_at FROM requests ORDER BY created_at');
    console.log(`Found ${requests.length} requests`);

    if (requests.length === 0) {
      console.log('No requests found to update');
      return;
    }

    // Update requests to have more recent dates
    // We'll spread them over the last 30 days
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    console.log('🔄 Updating request dates...');
    
    for (let i = 0; i < requests.length; i++) {
      const request = requests[i];
      
      // Calculate a date between 30 days ago and now
      const dayOffset = Math.floor((i / requests.length) * 30);
      const newDate = new Date(thirtyDaysAgo.getTime() + (dayOffset * 24 * 60 * 60 * 1000));
      
      // Add some random hours/minutes to make it more realistic
      newDate.setHours(Math.floor(Math.random() * 24));
      newDate.setMinutes(Math.floor(Math.random() * 60));
      
      await connection.query(
        'UPDATE requests SET created_at = ?, updated_at = ? WHERE id = ?',
        [newDate, newDate, request.id]
      );
      
      console.log(`✅ Updated request ${request.id} to ${newDate.toISOString()}`);
    }

    console.log('🎉 All request dates updated successfully!');

    // Show updated requests
    console.log('\n📊 Updated requests:');
    const [updatedRequests] = await connection.query(
      'SELECT id, project_name, created_at FROM requests ORDER BY created_at DESC LIMIT 10'
    );
    
    updatedRequests.forEach(req => {
      console.log(`- ${req.project_name}: ${req.created_at}`);
    });

  } catch (error) {
    console.error('❌ Error updating request dates:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the update
updateRequestDates();
