#!/usr/bin/env node

const https = require('https');
const http = require('http');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testEndpoint(url, name) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          log(`✅ ${name}: OK (${res.statusCode})`, 'green');
          resolve({ success: true, status: res.statusCode, data });
        } else {
          log(`⚠️  ${name}: Warning (${res.statusCode})`, 'yellow');
          resolve({ success: false, status: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      log(`❌ ${name}: Failed - ${error.message}`, 'red');
      resolve({ success: false, error: error.message });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      log(`⏰ ${name}: Timeout`, 'yellow');
      resolve({ success: false, error: 'Timeout' });
    });
  });
}

async function main() {
  log('📋 STEP 6: Final Testing & Verification', 'cyan');
  log('=======================================', 'cyan');

  log('\n🎯 GOAL: Verify complete staging environment is working', 'yellow');

  log('\n✅ Prerequisites:', 'green');
  log('- ✅ All previous steps completed', 'blue');
  log('- ✅ Backend and frontend URLs obtained', 'blue');
  log('- ✅ CORS configured', 'blue');

  log('\n📋 6.1 Manual Testing Checklist:', 'yellow');
  log('================================', 'yellow');

  log('\n🔹 Backend Testing:', 'cyan');
  log('Replace [backend-url] with your actual backend URL:', 'blue');
  log('', 'blue');
  log('   📝 Health Check:', 'blue');
  log('   https://[backend-url]/health', 'blue');
  log('   Expected: {"success": true, "message": "Railway server is running"}', 'blue');
  log('', 'blue');
  log('   📝 Database Test:', 'blue');
  log('   https://[backend-url]/api/test-connection', 'blue');
  log('   Expected: {"success": true, "message": "Database connection successful"}', 'blue');

  log('\n🔹 Frontend Testing:', 'cyan');
  log('Replace [frontend-url] with your actual Netlify staging URL:', 'blue');
  log('', 'blue');
  log('   📝 Page Load Test:', 'blue');
  log('   https://[frontend-url]', 'blue');
  log('   Expected: Page loads without errors', 'blue');
  log('', 'blue');
  log('   📝 Console Check:', 'blue');
  log('   Open browser console (F12)', 'blue');
  log('   Expected: No CORS errors, no API errors', 'blue');

  log('\n🔹 Integration Testing:', 'cyan');
  log('Test frontend ↔ backend communication:', 'blue');
  log('', 'blue');
  log('   📝 Login/Register:', 'blue');
  log('   Try to login or register a user', 'blue');
  log('   Expected: API calls work, no CORS errors', 'blue');
  log('', 'blue');
  log('   📝 Data Loading:', 'blue');
  log('   Navigate through the app', 'blue');
  log('   Expected: Data loads from backend', 'blue');

  log('\n🧪 6.2 Automated Testing (Optional):', 'yellow');
  log('====================================', 'yellow');

  log('\n💡 You can test some endpoints automatically:', 'blue');
  log('Update the URLs below and run this script again:', 'blue');

  // Example endpoints for testing
  const testEndpoints = [
    'https://[your-backend-url]/health',
    'https://[your-backend-url]/api/test-connection',
    'https://[your-frontend-url]'
  ];

  log('\n📝 Example test URLs (update with your actual URLs):', 'yellow');
  testEndpoints.forEach((url, index) => {
    log(`${index + 1}. ${url}`, 'blue');
  });

  log('\n🔧 6.3 Environment Verification:', 'yellow');
  log('=================================', 'yellow');

  log('\n✅ Staging Environment Checklist:', 'cyan');
  log('□ Backend staging URL accessible', 'blue');
  log('□ Backend health endpoint returns success', 'blue');
  log('□ Backend database connection works', 'blue');
  log('□ Frontend staging URL accessible', 'blue');
  log('□ Frontend loads without errors', 'blue');
  log('□ No CORS errors in browser console', 'blue');
  log('□ API calls work from frontend to backend', 'blue');
  log('□ Login/register functionality works', 'blue');
  log('□ Data loads correctly in frontend', 'blue');
  log('□ Environment variables configured correctly', 'blue');

  log('\n🔄 6.4 Workflow Testing:', 'yellow');
  log('========================', 'yellow');

  log('\n📝 Test the development workflow:', 'blue');
  log('1. Make a small change to the code', 'blue');
  log('2. Commit to staging branch:', 'blue');
  log('   git add .', 'blue');
  log('   git commit -m "test: staging workflow"', 'blue');
  log('   git push origin staging', 'blue');
  log('3. Watch Netlify auto-deploy the changes', 'blue');
  log('4. Verify changes appear on staging URL', 'blue');

  log('\n🎉 6.5 Success Criteria:', 'green');
  log('========================', 'green');

  log('\n✅ Complete Success Indicators:', 'yellow');
  log('🟢 Backend staging: Healthy and accessible', 'green');
  log('🟢 Database: Connected and working', 'green');
  log('🟢 Frontend staging: Loading without errors', 'green');
  log('🟢 CORS: No errors, API calls working', 'green');
  log('🟢 Authentication: Login/register working', 'green');
  log('🟢 Data flow: Frontend ↔ Backend communication', 'green');
  log('🟢 Deployment: Auto-deploy on git push working', 'green');

  log('\n🌐 6.6 Final URLs Summary:', 'cyan');
  log('==========================', 'cyan');

  log('\n📋 Your staging environment URLs:', 'yellow');
  log('   Backend:  https://[backend-staging-production-xxxx].up.railway.app', 'blue');
  log('   Frontend: https://[site-id]--staging.netlify.app', 'blue');
  log('   Database: MySQL on Railway (staging)', 'blue');

  log('\n📋 Production URLs (unchanged):', 'yellow');
  log('   Backend:  https://gudangmitra-production.up.railway.app', 'blue');
  log('   Frontend: https://gudang.netlify.app', 'blue');
  log('   Database: MySQL on Railway (production)', 'blue');

  log('\n🔄 6.7 Development Workflow:', 'cyan');
  log('============================', 'cyan');

  log('\n📝 Going forward, your workflow will be:', 'yellow');
  log('', 'blue');
  log('🔹 Development → Staging:', 'blue');
  log('   git checkout staging', 'blue');
  log('   # make changes', 'blue');
  log('   git add . && git commit -m "feat: new feature"', 'blue');
  log('   git push origin staging  # Auto-deploys to staging', 'blue');
  log('   # Test on staging environment', 'blue');
  log('', 'blue');
  log('🔹 Staging → Production:', 'blue');
  log('   git checkout main', 'blue');
  log('   git merge staging', 'blue');
  log('   git push origin main     # Auto-deploys to production', 'blue');

  log('\n🆘 6.8 Troubleshooting Guide:', 'cyan');
  log('=============================', 'cyan');

  log('\n❌ If something is not working:', 'red');
  log('1. Check Railway service status and logs', 'blue');
  log('2. Check Netlify deployment status and logs', 'blue');
  log('3. Verify all environment variables are set', 'blue');
  log('4. Test endpoints individually', 'blue');
  log('5. Check browser console for errors', 'blue');
  log('6. Verify CORS_ORIGIN matches frontend URL exactly', 'blue');

  log('\n📚 6.9 Helpful Commands:', 'cyan');
  log('========================', 'cyan');

  log('\n🔍 Debug commands:', 'yellow');
  log('   git remote -v                    # Check Git remotes', 'blue');
  log('   git branch -a                    # Check all branches', 'blue');
  log('   curl [backend-url]/health        # Test backend', 'blue');
  log('   curl [backend-url]/api/test-connection  # Test database', 'blue');

  log('\n🎉 CONGRATULATIONS!', 'green');
  log('===================', 'green');

  log('\n🏆 If all tests pass, you now have:', 'yellow');
  log('✅ Complete staging environment', 'green');
  log('✅ Isolated from production', 'green');
  log('✅ Auto-deployment workflow', 'green');
  log('✅ Professional development setup', 'green');

  log('\n🚀 You can now:', 'cyan');
  log('• Develop new features safely in staging', 'blue');
  log('• Test changes before production deployment', 'blue');
  log('• Have confidence in your deployments', 'blue');
  log('• Scale your development workflow', 'blue');

  log('\n✅ Setup Complete!', 'green');
  log('Happy coding! 🎉', 'yellow');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, testEndpoint };
