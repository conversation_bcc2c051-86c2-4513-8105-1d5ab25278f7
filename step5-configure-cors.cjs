#!/usr/bin/env node

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('📋 STEP 5: Configure CORS (Connect Frontend ↔ Backend)', 'cyan');
  log('=======================================================', 'cyan');

  log('\n🎯 GOAL: Allow staging frontend to communicate with staging backend', 'yellow');

  log('\n✅ Prerequisites:', 'green');
  log('- ✅ Backend staging URL working', 'blue');
  log('- ✅ Frontend staging URL from Netlify', 'blue');
  log('- ✅ Both services deployed', 'blue');

  log('\n🔗 5.1 Get Your URLs:', 'yellow');
  log('You should have these URLs from previous steps:', 'blue');
  log('', 'blue');
  log('   📝 Backend URL: ________________', 'blue');
  log('   Format: https://backend-staging-production-xxxx.up.railway.app', 'blue');
  log('', 'blue');
  log('   📝 Frontend URL: ________________', 'blue');
  log('   Format: https://[site-id]--staging.netlify.app', 'blue');

  log('\n🌐 5.2 Update CORS in Railway Backend:', 'yellow');
  log('1. Open Railway dashboard:', 'blue');
  log('   https://railway.com/project/************************************', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Click "Variables" tab', 'blue');
  log('4. Find "CORS_ORIGIN" variable', 'blue');
  log('5. Update its value to your Netlify staging URL', 'blue');

  log('\n📝 5.3 CORS_ORIGIN Update Example:', 'yellow');
  log('Current value (placeholder):', 'blue');
  log('   CORS_ORIGIN = https://gudang-staging.netlify.app', 'red');
  log('', 'blue');
  log('Update to your actual Netlify staging URL:', 'blue');
  log('   CORS_ORIGIN = https://[your-actual-staging-url]', 'green');
  log('', 'blue');
  log('Example:', 'blue');
  log('   CORS_ORIGIN = https://abc123--staging.netlify.app', 'green');

  log('\n🔄 5.4 Redeploy Backend Service:', 'yellow');
  log('After updating CORS_ORIGIN:', 'blue');
  log('1. In backend-staging service', 'blue');
  log('2. Click "Deployments" tab', 'blue');
  log('3. Click "Redeploy" on latest deployment', 'blue');
  log('4. Wait for deployment to complete', 'blue');

  log('\n🧪 5.5 Test CORS Configuration:', 'yellow');
  log('1. Open your staging frontend URL', 'blue');
  log('2. Open browser console (F12)', 'blue');
  log('3. Try to login or make API calls', 'blue');
  log('4. Check for CORS errors in console', 'blue');

  log('\n✅ 5.6 Expected Results:', 'yellow');
  log('✅ No CORS errors in browser console', 'blue');
  log('✅ API calls work from frontend to backend', 'blue');
  log('✅ Login/register functionality works', 'blue');
  log('✅ Data loads correctly in frontend', 'blue');

  log('\n💡 CORS TESTING CHECKLIST:', 'cyan');
  log('===========================', 'cyan');

  log('\n🔹 Test 1: Health Check from Frontend', 'yellow');
  log('1. Open staging frontend', 'blue');
  log('2. Open browser console', 'blue');
  log('3. Run: fetch("[backend-url]/health").then(r=>r.json()).then(console.log)', 'blue');
  log('4. Should return success response', 'blue');

  log('\n🔹 Test 2: API Call from Frontend', 'yellow');
  log('1. Try login functionality', 'blue');
  log('2. Check network tab in browser', 'blue');
  log('3. Verify API calls complete successfully', 'blue');
  log('4. No CORS errors in console', 'blue');

  log('\n🔹 Test 3: Full Application Flow', 'yellow');
  log('1. Navigate through the app', 'blue');
  log('2. Try different features', 'blue');
  log('3. Check all API interactions work', 'blue');
  log('4. Verify data loads correctly', 'blue');

  log('\n🆘 TROUBLESHOOTING:', 'cyan');
  log('==================', 'cyan');

  log('\n❌ CORS errors still appearing:', 'red');
  log('   → Double-check CORS_ORIGIN matches exact Netlify URL', 'blue');
  log('   → Ensure no trailing slash in CORS_ORIGIN', 'blue');
  log('   → Redeploy backend after changing CORS_ORIGIN', 'blue');
  log('   → Check Railway deployment logs for errors', 'blue');

  log('\n❌ "Access-Control-Allow-Origin" error:', 'red');
  log('   → CORS_ORIGIN doesn\'t match frontend URL', 'blue');
  log('   → Copy exact URL from Netlify (including https://)', 'blue');
  log('   → No extra characters or spaces', 'blue');

  log('\n❌ API calls fail with network errors:', 'red');
  log('   → Check if backend URL is accessible', 'blue');
  log('   → Verify backend service is running', 'blue');
  log('   → Test backend endpoints directly in browser', 'blue');

  log('\n❌ Frontend shows "API connection failed":', 'red');
  log('   → Check .env.staging has correct backend URL', 'blue');
  log('   → Verify frontend is using staging environment', 'blue');
  log('   → Redeploy frontend after URL changes', 'blue');

  log('\n📋 COMMON CORS MISTAKES:', 'cyan');
  log('========================', 'cyan');

  log('\n❌ Wrong URL format:', 'red');
  log('   Wrong: gudang-staging.netlify.app', 'red');
  log('   Right: https://gudang-staging.netlify.app', 'green');

  log('\n❌ Trailing slash:', 'red');
  log('   Wrong: https://abc123--staging.netlify.app/', 'red');
  log('   Right: https://abc123--staging.netlify.app', 'green');

  log('\n❌ Mixed up URLs:', 'red');
  log('   Wrong: Using backend URL in CORS_ORIGIN', 'red');
  log('   Right: Using frontend URL in CORS_ORIGIN', 'green');

  log('\n📋 AFTER SUCCESSFUL CORS SETUP:', 'green');
  log('================================', 'green');

  log('\n✅ You should have:', 'yellow');
  log('   - No CORS errors in browser console', 'blue');
  log('   - Frontend can call backend APIs', 'blue');
  log('   - Login/register works', 'blue');
  log('   - Data loads correctly', 'blue');
  log('   - Full staging environment functional', 'blue');

  log('\n🚀 NEXT STEPS:', 'cyan');
  log('==============', 'cyan');

  log('\n📋 Progress so far:', 'yellow');
  log('1. ✅ Git repository connected', 'green');
  log('2. ✅ Railway database connected', 'green');
  log('3. ✅ Backend URL obtained and tested', 'green');
  log('4. ✅ Netlify staging deployment working', 'green');
  log('5. ✅ CORS configured (frontend ↔ backend)', 'green');
  log('6. 🔄 Final testing and verification', 'blue');

  log('\n📝 Continue to final step:', 'yellow');
  log('   node step6-final-testing.cjs', 'blue');
  log('   # OR follow COMPLETE-SETUP-CHECKLIST.md', 'blue');

  log('\n✅ Ready to configure CORS!', 'green');
  log('Update CORS_ORIGIN in Railway with your Netlify staging URL.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
