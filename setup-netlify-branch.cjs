#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

function main() {
  log('🌐 Setting up Netlify Branch Deployment', 'cyan');
  log('=======================================', 'cyan');

  // Check current branch
  const currentBranch = executeCommand('git branch --show-current', 'Checking current branch');
  log(`Current branch: ${currentBranch?.trim()}`, 'blue');

  // Ensure we're on staging branch
  if (!currentBranch?.includes('staging')) {
    log('Switching to staging branch...', 'yellow');
    executeCommand('git checkout staging', 'Switching to staging branch');
  }

  // Add any new files
  executeCommand('git add .', 'Adding new files');

  // Commit changes
  const commitResult = executeCommand('git commit -m "Setup staging environment configuration"', 'Committing changes');
  
  log('\n📋 Next Steps for Netlify Setup:', 'yellow');
  log('1. Push staging branch to your Git repository:', 'blue');
  log('   git push origin staging', 'blue');
  log('', 'blue');
  log('2. Go to your Netlify dashboard:', 'blue');
  log('   https://app.netlify.com/', 'blue');
  log('', 'blue');
  log('3. Select your site (gudang.netlify.app)', 'blue');
  log('', 'blue');
  log('4. Go to Site Settings > Build & Deploy', 'blue');
  log('', 'blue');
  log('5. Scroll down to "Deploy contexts"', 'blue');
  log('', 'blue');
  log('6. Enable "Branch deploys"', 'blue');
  log('', 'blue');
  log('7. Add "staging" to the branch deploy list', 'blue');
  log('', 'blue');
  log('8. Save settings', 'blue');

  log('\n🚀 After Netlify Setup:', 'cyan');
  log('1. Netlify will automatically deploy staging branch', 'blue');
  log('2. You will get a staging URL like:', 'blue');
  log('   https://[site-id]--staging.netlify.app', 'blue');
  log('   or', 'blue');
  log('   https://staging--[site-name].netlify.app', 'blue');
  log('', 'blue');
  log('3. Copy the staging URL for CORS configuration', 'blue');

  log('\n📝 Current Netlify Configuration:', 'yellow');
  if (fs.existsSync('netlify.toml')) {
    const netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    console.log(netlifyConfig);
  }

  log('\n⚡ Commands to Run:', 'cyan');
  log('# Push staging branch:', 'blue');
  log('git push origin staging', 'blue');
  log('', 'blue');
  log('# After getting Netlify staging URL, update CORS:', 'blue');
  log('railway variables --set "CORS_ORIGIN=https://[netlify-staging-url]"', 'blue');
  log('', 'blue');
  log('# Test everything:', 'blue');
  log('node test-staging-complete.cjs', 'blue');

  log('\n📋 Checklist:', 'yellow');
  log('□ Staging branch committed and ready', 'blue');
  log('□ Push staging branch to Git repository', 'blue');
  log('□ Enable branch deploys in Netlify', 'blue');
  log('□ Add "staging" branch to deploy list', 'blue');
  log('□ Get staging URL from Netlify', 'blue');
  log('□ Update CORS_ORIGIN in Railway', 'blue');

  log('\n✅ Ready to push staging branch!', 'green');
  log('Run: git push origin staging', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
