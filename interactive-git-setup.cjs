#!/usr/bin/env node

const { execSync } = require('child_process');
const readline = require('readline');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function main() {
  log('🚀 Interactive Git Repository Setup', 'cyan');
  log('===================================', 'cyan');

  log('\n📋 Current Status:', 'blue');
  log('- Local Git repository: ✅ Initialized', 'green');
  log('- Branch staging: ✅ Created and committed', 'green');
  log('- Remote repository: ❌ Not configured', 'red');

  log('\n🎯 We need to set up a remote Git repository.', 'yellow');
  log('This will allow Netlify to deploy your staging branch.', 'yellow');

  const choice = await askQuestion('\n📋 Choose an option:\n1. Create new GitHub repository (Recommended)\n2. Use existing repository\n3. Skip Git setup for now\n\nEnter your choice (1, 2, or 3): ');

  if (choice === '1') {
    await setupNewRepository();
  } else if (choice === '2') {
    await setupExistingRepository();
  } else if (choice === '3') {
    log('\n⚠️  Skipping Git setup.', 'yellow');
    log('You can set this up later using the manual guide.', 'yellow');
    log('Run: node setup-git-repository.cjs', 'blue');
  } else {
    log('\n❌ Invalid choice. Please run the script again.', 'red');
  }

  rl.close();
}

async function setupNewRepository() {
  log('\n🆕 Setting up NEW GitHub repository', 'cyan');
  log('===================================', 'cyan');

  log('\n📋 Step 1: Create GitHub Repository', 'yellow');
  log('1. Open this link: https://github.com/new', 'blue');
  log('2. Repository name: gudangsub-staging', 'blue');
  log('3. Description: Gudang Mitra - Staging Environment', 'blue');
  log('4. Choose Public or Private', 'blue');
  log('5. DO NOT check "Add a README file"', 'blue');
  log('6. DO NOT check "Add .gitignore"', 'blue');
  log('7. DO NOT check "Choose a license"', 'blue');
  log('8. Click "Create repository"', 'blue');

  await askQuestion('\n⏳ Press Enter after you have created the repository...');

  const username = await askQuestion('\n📝 Enter your GitHub username: ');
  const repoName = await askQuestion('📝 Enter repository name (or press Enter for "gudangsub-staging"): ') || 'gudangsub-staging';

  const repoUrl = `https://github.com/${username}/${repoName}.git`;
  
  log(`\n🔗 Repository URL: ${repoUrl}`, 'blue');
  
  const confirm = await askQuestion('\nIs this correct? (y/n): ');
  
  if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
    await connectRepository(repoUrl);
  } else {
    log('\n❌ Setup cancelled. Please run the script again.', 'red');
  }
}

async function setupExistingRepository() {
  log('\n🔄 Setting up EXISTING repository', 'cyan');
  log('=================================', 'cyan');

  const repoUrl = await askQuestion('\n📝 Enter your repository URL (e.g., https://github.com/username/repo.git): ');
  
  if (!repoUrl) {
    log('\n❌ No URL provided. Setup cancelled.', 'red');
    return;
  }

  log(`\n🔗 Repository URL: ${repoUrl}`, 'blue');
  
  const confirm = await askQuestion('\nIs this correct? (y/n): ');
  
  if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
    await connectRepository(repoUrl);
  } else {
    log('\n❌ Setup cancelled. Please run the script again.', 'red');
  }
}

async function connectRepository(repoUrl) {
  log('\n🔗 Connecting to repository...', 'cyan');
  
  // Add remote
  const addRemoteResult = executeCommand(`git remote add origin ${repoUrl}`, 'Adding remote origin');
  
  if (addRemoteResult === null) {
    log('\n❌ Failed to add remote. Repository might already be connected.', 'red');
    
    // Check if remote already exists
    const remotes = executeCommand('git remote -v', 'Checking existing remotes');
    if (remotes) {
      log('\nExisting remotes:', 'blue');
      console.log(remotes);
      
      const updateRemote = await askQuestion('\nDo you want to update the remote URL? (y/n): ');
      if (updateRemote.toLowerCase() === 'y') {
        executeCommand(`git remote set-url origin ${repoUrl}`, 'Updating remote URL');
      }
    }
  }

  // Switch to main branch and push
  log('\n📤 Pushing branches to repository...', 'cyan');
  
  executeCommand('git checkout main', 'Switching to main branch');
  const pushMainResult = executeCommand('git push -u origin main', 'Pushing main branch');
  
  if (pushMainResult !== null) {
    log('✅ Main branch pushed successfully!', 'green');
    
    // Push staging branch
    executeCommand('git checkout staging', 'Switching to staging branch');
    const pushStagingResult = executeCommand('git push -u origin staging', 'Pushing staging branch');
    
    if (pushStagingResult !== null) {
      log('\n🎉 SUCCESS! Both branches pushed successfully!', 'green');
      
      log('\n📋 Next Steps:', 'cyan');
      log('1. ✅ Git repository setup complete', 'green');
      log('2. 🔄 Next: Railway database setup', 'yellow');
      log('3. 🔄 Next: Netlify branch deploy setup', 'yellow');
      
      log('\n🚀 Ready for next step!', 'green');
      log('Run: node manual-setup-guide.cjs', 'blue');
      log('Or follow: COMPLETE-SETUP-CHECKLIST.md', 'blue');
      
    } else {
      log('\n⚠️  Staging branch push failed, but main branch is OK.', 'yellow');
      log('You can try pushing staging branch manually later.', 'yellow');
    }
  } else {
    log('\n❌ Failed to push main branch.', 'red');
    log('Please check your repository permissions and try again.', 'yellow');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
