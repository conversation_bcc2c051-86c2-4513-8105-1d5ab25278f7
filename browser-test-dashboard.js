// Script untuk test dashboard di browser console
// Copy dan paste script ini di browser console setelah login

console.log("🔍 Testing Dashboard Fallback Service...");

// Test individual endpoints
async function testEndpoints() {
  const baseUrl = 'https://backend-staging-production.up.railway.app';
  
  console.log("📡 Testing individual endpoints...");
  
  try {
    // Test users endpoint
    console.log("Testing /api/users...");
    const usersResponse = await fetch(`${baseUrl}/api/users`);
    const usersData = await usersResponse.json();
    console.log("✅ Users:", usersData.success ? `${usersData.data.length} users` : "Failed");
    
    // Test items endpoint
    console.log("Testing /api/items...");
    const itemsResponse = await fetch(`${baseUrl}/api/items`);
    const itemsData = await itemsResponse.json();
    console.log("✅ Items:", itemsData.success ? `${itemsData.data.length} items` : "Failed");
    
    // Test requests endpoint
    console.log("Testing /api/requests...");
    const requestsResponse = await fetch(`${baseUrl}/api/requests`);
    const requestsData = await requestsResponse.json();
    console.log("✅ Requests:", requestsData.success ? `${requestsData.data.length} requests` : "Failed");
    
    // Test dashboard endpoint (should fail)
    console.log("Testing /api/dashboard/stats...");
    const dashboardResponse = await fetch(`${baseUrl}/api/dashboard/stats`);
    console.log("❌ Dashboard API:", dashboardResponse.status, dashboardResponse.statusText);
    
    if (usersData.success && itemsData.success && requestsData.success) {
      console.log("🎉 All required endpoints are working!");
      console.log("📊 Calculating fallback stats...");
      
      // Calculate stats manually
      const users = usersData.data;
      const items = itemsData.data;
      const requests = requestsData.data;
      
      const stats = {
        totalUsers: users.length,
        totalItems: items.length,
        totalRequests: requests.length,
        usersByRole: users.reduce((acc, user) => {
          const role = user.role || 'user';
          acc[role] = (acc[role] || 0) + 1;
          return acc;
        }, { admin: 0, manager: 0, user: 0 }),
        requestsByStatus: requests.reduce((acc, request) => {
          const status = request.status || 'pending';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, { pending: 0, approved: 0, denied: 0, fulfilled: 0 })
      };
      
      console.log("📊 Calculated Dashboard Stats:", stats);
      return stats;
    }
    
  } catch (error) {
    console.error("❌ Error testing endpoints:", error);
  }
}

// Test dashboard service directly
async function testDashboardService() {
  console.log("🔍 Testing dashboard service directly...");
  
  try {
    // Try to access the dashboard service from window object
    if (window.dashboardService) {
      console.log("✅ Dashboard service found on window object");
      const stats = await window.dashboardService.getDashboardStats();
      console.log("📊 Dashboard service result:", stats);
    } else {
      console.log("❌ Dashboard service not found on window object");
    }
  } catch (error) {
    console.error("❌ Error testing dashboard service:", error);
  }
}

// Run tests
testEndpoints().then(() => {
  console.log("✅ Endpoint tests completed");
  return testDashboardService();
}).then(() => {
  console.log("✅ All tests completed");
}).catch(error => {
  console.error("❌ Test failed:", error);
});

// Instructions
console.log(`
📋 INSTRUCTIONS:
1. Make sure you're logged in to the staging app
2. Copy and paste this entire script in the browser console
3. Check the console output for any errors
4. If endpoints work but dashboard shows 0, there might be a React state issue

🔧 TROUBLESHOOTING:
- If you see CORS errors, the backend needs CORS configuration
- If you see 401 errors, authentication might be required for some endpoints
- If endpoints work but dashboard doesn't update, try refreshing the page
`);
