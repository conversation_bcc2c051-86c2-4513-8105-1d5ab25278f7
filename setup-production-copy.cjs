#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🗄️  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

function main() {
  heading('Setup Copy from Production Database');
  
  log('\n📋 Untuk copy dari database production Railway:', 'blue');
  
  log('\n🔍 Step 1: Identifikasi Database Production', 'cyan');
  log('Apakah Anda punya:', 'blue');
  log('a) Project Railway production yang terpisah?', 'blue');
  log('b) Database production di project yang sama?', 'blue');
  log('c) Database production di server lain (non-Railway)?', 'blue');
  
  log('\n🔧 Step 2: Dapatkan Connection Details', 'cyan');
  
  log('\n📝 Untuk Railway Production Database:', 'yellow');
  log('1. Buka Railway dashboard: https://railway.app/dashboard', 'blue');
  log('2. Pilih project production Anda', 'blue');
  log('3. Klik pada MySQL/Database service', 'blue');
  log('4. Buka tab "Connect"', 'blue');
  log('5. Copy connection details', 'blue');
  
  log('\n📊 Connection details yang dibutuhkan:', 'cyan');
  log('- Host (contoh: mysql.railway.internal atau external host)', 'blue');
  log('- Port (biasanya 3306 internal, atau port khusus external)', 'blue');
  log('- Username (biasanya root)', 'blue');
  log('- Password', 'blue');
  log('- Database name (biasanya railway)', 'blue');
  
  log('\n🔧 Step 3: Update Configuration', 'cyan');
  log('Edit file: database-copy-config.env', 'blue');
  log('', 'blue');
  log('SOURCE_DB_HOST=your-production-host', 'blue');
  log('SOURCE_DB_PORT=your-production-port', 'blue');
  log('SOURCE_DB_USER=root', 'blue');
  log('SOURCE_DB_PASSWORD=your-production-password', 'blue');
  log('SOURCE_DB_NAME=railway', 'blue');
  log('SOURCE_DB_SSL=true', 'blue');
  
  log('\n📋 Contoh untuk Railway Production:', 'yellow');
  log('# Jika production menggunakan internal host:', 'blue');
  log('SOURCE_DB_HOST=mysql.railway.internal', 'blue');
  log('SOURCE_DB_PORT=3306', 'blue');
  log('', 'blue');
  log('# Jika production menggunakan external host:', 'blue');
  log('SOURCE_DB_HOST=containers-us-west-xxx.railway.app', 'blue');
  log('SOURCE_DB_PORT=6543', 'blue');
  
  log('\n🚀 Step 4: Test & Copy', 'cyan');
  log('node test-database-connection.cjs  # Test koneksi', 'blue');
  log('node copy-database.cjs copy source staging  # Copy database', 'blue');
  
  log('\n💡 Tips:', 'yellow');
  log('- Pastikan production database dapat diakses dari luar', 'yellow');
  log('- Jika menggunakan internal host, mungkin perlu VPN/tunnel', 'yellow');
  log('- External host biasanya lebih mudah diakses', 'yellow');
  log('- Backup staging database sebelum copy (opsional)', 'yellow');
  
  log('\n⚠️  Peringatan:', 'red');
  log('- Copy akan MENGGANTI semua data di staging', 'red');
  log('- Pastikan production database tidak sedang maintenance', 'red');
  log('- Proses copy bisa memakan waktu tergantung ukuran data', 'red');
  
  log('\n🔗 Helpful Links:', 'cyan');
  log('Railway Dashboard: https://railway.app/dashboard', 'blue');
  log('Railway Docs: https://docs.railway.app/databases/mysql', 'blue');
  
  // Check if config file exists
  if (fs.existsSync('database-copy-config.env')) {
    log('\n✅ Configuration file found: database-copy-config.env', 'green');
    log('You can edit this file with your production database details.', 'green');
  } else {
    log('\n📝 Creating configuration template...', 'yellow');
    try {
      execSync('cp database-copy-config.env.template database-copy-config.env');
      log('✅ Created: database-copy-config.env', 'green');
      log('Please edit this file with your production database details.', 'green');
    } catch (error) {
      log('❌ Failed to create config file. Please copy manually:', 'red');
      log('cp database-copy-config.env.template database-copy-config.env', 'blue');
    }
  }
  
  log('\n🎯 Next Steps:', 'green');
  log('1. Edit database-copy-config.env with production details', 'blue');
  log('2. Run: node test-database-connection.cjs', 'blue');
  log('3. Run: node copy-database.cjs copy source staging', 'blue');
}

if (require.main === module) {
  main();
}

module.exports = { main };
