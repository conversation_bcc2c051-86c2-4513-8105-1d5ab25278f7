#!/usr/bin/env node

const https = require('https');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`📊  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

// Fetch data from API
function fetchData(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          if (res.statusCode === 200 && jsonData.success) {
            resolve(jsonData.data);
          } else {
            reject(new Error(`API Error: ${res.statusCode} - ${jsonData.message || 'Unknown error'}`));
          }
        } catch (error) {
          reject(new Error(`Parse Error: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`Network Error: ${error.message}`));
    });
  });
}

// Calculate dashboard statistics manually
async function calculateDashboardStats() {
  const baseUrl = 'https://backend-staging-production.up.railway.app';
  
  try {
    log('🔍 Fetching data from individual endpoints...', 'cyan');
    
    // Fetch data from existing endpoints
    const [usersData, itemsData, requestsData] = await Promise.all([
      fetchData(`${baseUrl}/api/users`),
      fetchData(`${baseUrl}/api/items`),
      fetchData(`${baseUrl}/api/requests`)
    ]);

    log(`✅ Users data: ${usersData.length} users`, 'green');
    log(`✅ Items data: ${itemsData.length} items`, 'green');
    log(`✅ Requests data: ${requestsData.length} requests`, 'green');

    // Calculate user statistics
    const totalUsers = usersData.length;
    const usersByRole = usersData.reduce((acc, user) => {
      const role = user.role || 'user';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, { admin: 0, manager: 0, user: 0 });

    // Calculate item statistics
    const totalItems = itemsData.length;
    const totalQuantity = itemsData.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
    const lowStockItems = itemsData.filter(item => 
      parseInt(item.quantity) <= parseInt(item.minQuantity || 0)
    ).length;
    
    const categories = new Set(itemsData.map(item => item.category).filter(Boolean));
    const totalCategories = categories.size;

    // Calculate request statistics
    const totalRequests = requestsData.length;
    
    const requestsByStatus = requestsData.reduce((acc, request) => {
      const status = request.status || 'pending';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, { pending: 0, approved: 0, denied: 0, fulfilled: 0 });

    // Calculate recent requests (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentRequests = requestsData.filter(request => {
      const createdAt = new Date(request.created_at || request.createdAt);
      return createdAt >= sevenDaysAgo;
    }).length;

    const dashboardStats = {
      // User statistics
      totalUsers,
      usersByRole,

      // Item statistics
      totalItems,
      totalQuantity,
      lowStockItems,
      totalCategories,

      // Request statistics
      totalRequests,
      requestsByStatus,
      recentRequests
    };

    return dashboardStats;
    
  } catch (error) {
    log(`❌ Error calculating dashboard stats: ${error.message}`, 'red');
    throw error;
  }
}

async function main() {
  heading('Test Fallback Dashboard Calculation');
  
  try {
    const stats = await calculateDashboardStats();
    
    log('\n📊 Calculated Dashboard Statistics:', 'cyan');
    log('', 'reset');
    
    // User Statistics
    log('👥 USER STATISTICS:', 'blue');
    log(`   Total Users: ${stats.totalUsers}`, 'green');
    log(`   Admin: ${stats.usersByRole.admin}`, 'blue');
    log(`   Manager: ${stats.usersByRole.manager}`, 'blue');
    log(`   User: ${stats.usersByRole.user}`, 'blue');
    
    // Item Statistics
    log('\n📦 ITEM STATISTICS:', 'blue');
    log(`   Total Items: ${stats.totalItems}`, 'green');
    log(`   Total Quantity: ${stats.totalQuantity}`, 'blue');
    log(`   Low Stock Items: ${stats.lowStockItems}`, 'yellow');
    log(`   Total Categories: ${stats.totalCategories}`, 'blue');
    
    // Request Statistics
    log('\n📋 REQUEST STATISTICS:', 'blue');
    log(`   Total Requests: ${stats.totalRequests}`, 'green');
    log(`   Pending: ${stats.requestsByStatus.pending}`, 'yellow');
    log(`   Approved: ${stats.requestsByStatus.approved}`, 'green');
    log(`   Denied: ${stats.requestsByStatus.denied}`, 'red');
    log(`   Fulfilled: ${stats.requestsByStatus.fulfilled}`, 'blue');
    log(`   Recent (7 days): ${stats.recentRequests}`, 'cyan');
    
    log('\n🎉 Fallback dashboard calculation successful!', 'green');
    log('These statistics should now appear in the staging frontend dashboard.', 'green');
    
  } catch (error) {
    log(`\n❌ Test failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { calculateDashboardStats, main };
