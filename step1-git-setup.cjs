#!/usr/bin/env node

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('📋 STEP 1: Git Repository Setup', 'cyan');
  log('===============================', 'cyan');

  log('\n✅ Current Status:', 'green');
  log('- Local Git repository: ✅ Initialized', 'blue');
  log('- Branch main: ✅ Created', 'blue');
  log('- Branch staging: ✅ Created and ready', 'blue');
  log('- All files: ✅ Committed', 'blue');
  log('- Remote repository: ❌ Need to setup', 'red');

  log('\n🎯 GOAL: Connect to GitHub repository so Netlify can deploy', 'yellow');

  log('\n📋 OPTION 1: Create New GitHub Repository (RECOMMENDED)', 'cyan');
  log('=====================================================', 'cyan');

  log('\n🌐 1.1 Create Repository on GitHub:', 'yellow');
  log('1. Open: https://github.com/new', 'blue');
  log('2. Repository name: gudangsub-staging', 'blue');
  log('3. Description: Gudang Mitra - Staging Environment', 'blue');
  log('4. Choose Public or Private (your preference)', 'blue');
  log('5. ⚠️  IMPORTANT: DO NOT check any of these boxes:', 'red');
  log('   ❌ Add a README file', 'red');
  log('   ❌ Add .gitignore', 'red');
  log('   ❌ Choose a license', 'red');
  log('6. Click "Create repository"', 'blue');

  log('\n🔗 1.2 After creating repository, GitHub will show commands like:', 'yellow');
  log('   git remote add origin https://github.com/[username]/gudangsub-staging.git', 'blue');
  log('   git branch -M main', 'blue');
  log('   git push -u origin main', 'blue');

  log('\n⚡ 1.3 Copy YOUR repository URL and run these commands:', 'yellow');
  log('   (Replace [username] with your actual GitHub username)', 'blue');
  log('', 'blue');
  log('   git remote add origin https://github.com/[username]/gudangsub-staging.git', 'blue');
  log('   git checkout main', 'blue');
  log('   git push -u origin main', 'blue');
  log('   git checkout staging', 'blue');
  log('   git push -u origin staging', 'blue');

  log('\n📋 OPTION 2: Use Existing Repository', 'cyan');
  log('===================================', 'cyan');

  log('\n🔄 2.1 If you already have a repository:', 'yellow');
  log('   git remote add origin [your-repository-url]', 'blue');
  log('   git checkout main', 'blue');
  log('   git push -u origin main', 'blue');
  log('   git checkout staging', 'blue');
  log('   git push -u origin staging', 'blue');

  log('\n✅ 2.2 Verify Setup:', 'yellow');
  log('   git remote -v', 'blue');
  log('   (Should show your repository URL)', 'blue');

  log('\n🎯 EXAMPLE COMMANDS (Replace with your info):', 'cyan');
  log('============================================', 'cyan');

  log('\n📝 If your GitHub username is "johndoe":', 'yellow');
  log('   git remote add origin https://github.com/johndoe/gudangsub-staging.git', 'blue');
  log('   git checkout main', 'blue');
  log('   git push -u origin main', 'blue');
  log('   git checkout staging', 'blue');
  log('   git push -u origin staging', 'blue');

  log('\n🆘 TROUBLESHOOTING:', 'cyan');
  log('==================', 'cyan');

  log('\n❌ "Permission denied" error:', 'red');
  log('   → You need to authenticate with GitHub', 'blue');
  log('   → Use GitHub Desktop, or set up SSH keys, or use personal access token', 'blue');

  log('\n❌ "Repository not found" error:', 'red');
  log('   → Check the repository URL is correct', 'blue');
  log('   → Make sure the repository exists on GitHub', 'blue');

  log('\n❌ "Remote origin already exists" error:', 'red');
  log('   → Run: git remote remove origin', 'blue');
  log('   → Then try adding the remote again', 'blue');

  log('\n📋 AFTER SUCCESSFUL SETUP:', 'green');
  log('==========================', 'green');

  log('\n✅ You should see:', 'yellow');
  log('   - Both main and staging branches on GitHub', 'blue');
  log('   - All your project files in the repository', 'blue');
  log('   - Repository ready for Netlify connection', 'blue');

  log('\n🚀 NEXT STEPS:', 'cyan');
  log('==============', 'cyan');

  log('\n📋 After Git setup is complete:', 'yellow');
  log('1. ✅ Git repository connected', 'green');
  log('2. 🔄 Setup Railway database (Step 2)', 'blue');
  log('3. 🔄 Setup Netlify branch deploy (Step 3)', 'blue');
  log('4. 🔄 Configure CORS (Step 4)', 'blue');
  log('5. 🔄 Test everything (Step 5)', 'blue');

  log('\n📚 Helper Commands:', 'cyan');
  log('==================', 'cyan');

  log('\n🔍 Check current status:', 'yellow');
  log('   git status', 'blue');
  log('   git remote -v', 'blue');
  log('   git branch -a', 'blue');

  log('\n📝 Continue to next step:', 'yellow');
  log('   node manual-setup-guide.cjs', 'blue');
  log('   # OR follow COMPLETE-SETUP-CHECKLIST.md', 'blue');

  log('\n✅ Ready to set up Git repository!', 'green');
  log('Choose Option 1 (new repo) or Option 2 (existing repo) above.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
