#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

function main() {
  log('🚀 Setting up Railway Staging Environment', 'cyan');
  log('=========================================', 'cyan');

  // Check if we're linked to the right project
  const status = executeCommand('railway status', 'Checking Railway status');
  if (!status || !status.includes('gudangmitra-staging')) {
    log('❌ Not linked to gudangmitra-staging project', 'red');
    log('Please run: railway link and select gudangmitra-staging', 'yellow');
    return;
  }

  log('\n📋 Current Railway Setup:', 'cyan');
  console.log(status);

  // Instructions for manual setup via Railway dashboard
  log('\n🔧 Manual Setup Required:', 'yellow');
  log('Please complete these steps in Railway dashboard:', 'yellow');
  log('1. Go to: https://railway.com/project/************************************', 'blue');
  log('2. Click on MySQL service', 'blue');
  log('3. Go to Variables tab', 'blue');
  log('4. Copy the database connection variables:', 'blue');
  log('   - MYSQL_HOST', 'blue');
  log('   - MYSQL_USER', 'blue');
  log('   - MYSQL_PASSWORD', 'blue');
  log('   - MYSQL_DATABASE', 'blue');
  log('   - MYSQL_PORT', 'blue');
  log('   - MYSQL_URL', 'blue');

  log('\n5. Go to backend-staging service', 'blue');
  log('6. Add these variables:', 'blue');
  log('   - DB_HOST = [MYSQL_HOST value]', 'blue');
  log('   - DB_USER = [MYSQL_USER value]', 'blue');
  log('   - DB_PASSWORD = [MYSQL_PASSWORD value]', 'blue');
  log('   - DB_NAME = [MYSQL_DATABASE value]', 'blue');
  log('   - DB_PORT = [MYSQL_PORT value] (usually 3306)', 'blue');
  log('   - DB_SSL = true', 'blue');

  // Create .env.staging with placeholder values
  const envStagingContent = `# Staging Environment Variables
# Update these values with actual Railway database credentials

DB_HOST=mysql-host-from-railway
DB_USER=mysql-user-from-railway
DB_PASSWORD=mysql-password-from-railway
DB_NAME=mysql-database-from-railway
DB_PORT=3306
DB_SSL=true

PORT=3002
NODE_ENV=staging

# CORS Configuration - Will be updated after Netlify staging setup
CORS_ORIGIN=https://gudang-staging.netlify.app

# Security
JWT_SECRET=staging-jwt-secret-key-here
`;

  fs.writeFileSync('server/.env.staging', envStagingContent);
  log('\n✅ Created server/.env.staging template', 'green');
  log('Please update it with actual Railway database credentials', 'yellow');

  // Show next steps
  log('\n📋 Next Steps:', 'cyan');
  log('1. Update server/.env.staging with actual database credentials', 'yellow');
  log('2. Deploy backend to Railway: railway up', 'yellow');
  log('3. Setup Netlify staging branch', 'yellow');
  log('4. Update CORS_ORIGIN with actual Netlify staging URL', 'yellow');
  log('5. Test the staging environment', 'yellow');

  log('\n🔗 Useful Links:', 'blue');
  log('- Railway Project: https://railway.com/project/************************************', 'blue');
  log('- Railway Docs: https://docs.railway.app/', 'blue');
}

if (require.main === module) {
  main();
}

module.exports = { main };
