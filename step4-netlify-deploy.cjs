#!/usr/bin/env node

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('📋 STEP 4: Setup Netlify Branch Deploy', 'cyan');
  log('======================================', 'cyan');

  log('\n🎯 GOAL: Enable Netlify to auto-deploy staging branch', 'yellow');

  log('\n✅ Prerequisites:', 'green');
  log('- ✅ Git repository with staging branch pushed', 'blue');
  log('- ✅ Railway backend working', 'blue');
  log('- ✅ Frontend config updated', 'blue');

  log('\n🌐 4.1 Connect Repository to Netlify:', 'yellow');
  log('1. Go to: https://app.netlify.com/', 'blue');
  log('2. Sign in to your Netlify account', 'blue');
  log('3. Look for your existing site (gudang.netlify.app)', 'blue');

  log('\n📋 4.2 Two Scenarios:', 'cyan');
  log('====================', 'cyan');

  log('\n🔹 SCENARIO A: Site already exists (most likely)', 'yellow');
  log('1. Click on your existing site', 'blue');
  log('2. Go to "Site Settings"', 'blue');
  log('3. Go to "Build & Deploy" section', 'blue');
  log('4. Scroll down to "Deploy contexts"', 'blue');
  log('5. Enable "Branch deploys"', 'blue');
  log('6. Add "staging" to the branch deploy list', 'blue');
  log('7. Save settings', 'blue');

  log('\n🔹 SCENARIO B: Need to connect new repository', 'yellow');
  log('1. Click "Add new site" → "Import an existing project"', 'blue');
  log('2. Choose "Deploy with GitHub"', 'blue');
  log('3. Select your repository (gudangsub-staging)', 'blue');
  log('4. Configure build settings:', 'blue');
  log('   - Branch to deploy: main', 'blue');
  log('   - Build command: npm run build', 'blue');
  log('   - Publish directory: dist', 'blue');
  log('5. Click "Deploy site"', 'blue');
  log('6. After deployment, enable branch deploys (see Scenario A)', 'blue');

  log('\n🚀 4.3 Enable Branch Deploys (Detailed Steps):', 'yellow');
  log('1. In your Netlify site dashboard', 'blue');
  log('2. Click "Site Settings" (top menu)', 'blue');
  log('3. Click "Build & Deploy" (left sidebar)', 'blue');
  log('4. Scroll down to "Deploy contexts" section', 'blue');
  log('5. Find "Branch deploys" setting', 'blue');
  log('6. Click "Edit settings"', 'blue');
  log('7. Enable "Deploy only the production branch and its deploy previews"', 'blue');
  log('8. OR enable "Deploy all branches" (if you want all branches)', 'blue');
  log('9. Add "staging" to the branch list', 'blue');
  log('10. Click "Save"', 'blue');

  log('\n📝 4.4 Trigger Staging Deployment:', 'yellow');
  log('After enabling branch deploys:', 'blue');
  log('1. Netlify will automatically detect staging branch', 'blue');
  log('2. It will start building and deploying', 'blue');
  log('3. You can watch the deployment in "Deploys" tab', 'blue');
  log('4. Deployment usually takes 2-5 minutes', 'blue');

  log('\n🔗 4.5 Get Staging URL:', 'yellow');
  log('1. Go to "Deploys" tab in your Netlify site', 'blue');
  log('2. Look for deployment from "staging" branch', 'blue');
  log('3. Click on the staging deployment', 'blue');
  log('4. Copy the staging URL', 'blue');

  log('\n📝 4.6 Expected Staging URL Formats:', 'yellow');
  log('   https://[site-id]--staging.netlify.app', 'blue');
  log('   OR', 'blue');
  log('   https://staging--[site-name].netlify.app', 'blue');
  log('   OR', 'blue');
  log('   https://[random-id].netlify.app', 'blue');

  log('\n🧪 4.7 Test Staging Frontend:', 'yellow');
  log('1. Open the staging URL in browser', 'blue');
  log('2. Check if the page loads without errors', 'blue');
  log('3. Open browser console (F12)', 'blue');
  log('4. Look for any error messages', 'blue');
  log('5. Try basic navigation', 'blue');

  log('\n💡 EXAMPLE WORKFLOW:', 'cyan');
  log('====================', 'cyan');

  log('\n📝 What happens after setup:', 'yellow');
  log('1. You push to staging branch: git push origin staging', 'blue');
  log('2. Netlify detects the push', 'blue');
  log('3. Netlify builds the staging branch', 'blue');
  log('4. Netlify deploys to staging URL', 'blue');
  log('5. You can test new features on staging', 'blue');
  log('6. When ready, merge staging → main for production', 'blue');

  log('\n🆘 TROUBLESHOOTING:', 'cyan');
  log('==================', 'cyan');

  log('\n❌ "Repository not found" in Netlify:', 'red');
  log('   → Make sure repository is public, or', 'blue');
  log('   → Grant Netlify access to private repositories', 'blue');
  log('   → Check GitHub integration in Netlify settings', 'blue');

  log('\n❌ "Build failed" error:', 'red');
  log('   → Check build logs in Netlify', 'blue');
  log('   → Verify package.json has correct build script', 'blue');
  log('   → Ensure all dependencies are in package.json', 'blue');

  log('\n❌ "Branch deploys not working":', 'red');
  log('   → Verify staging branch exists on GitHub', 'blue');
  log('   → Check branch deploy settings are enabled', 'blue');
  log('   → Try pushing a new commit to staging branch', 'blue');

  log('\n❌ "Environment variables not loading":', 'red');
  log('   → Check netlify.toml configuration', 'blue');
  log('   → Verify context.branch-deploy.environment settings', 'blue');
  log('   → Redeploy after configuration changes', 'blue');

  log('\n📋 AFTER SUCCESSFUL NETLIFY SETUP:', 'green');
  log('==================================', 'green');

  log('\n✅ You should have:', 'yellow');
  log('   - Staging branch auto-deploying to Netlify', 'blue');
  log('   - Staging URL accessible', 'blue');
  log('   - Frontend loading without errors', 'blue');
  log('   - Environment variables loading correctly', 'blue');

  log('\n🚀 NEXT STEPS:', 'cyan');
  log('==============', 'cyan');

  log('\n📋 Progress so far:', 'yellow');
  log('1. ✅ Git repository connected', 'green');
  log('2. ✅ Railway database connected', 'green');
  log('3. ✅ Backend URL obtained and tested', 'green');
  log('4. ✅ Netlify staging deployment working', 'green');
  log('5. 🔄 Configure CORS (connect frontend ↔ backend)', 'blue');
  log('6. 🔄 Test complete staging environment', 'blue');

  log('\n📝 Continue to next step:', 'yellow');
  log('   node step5-configure-cors.cjs', 'blue');
  log('   # OR follow COMPLETE-SETUP-CHECKLIST.md', 'blue');

  log('\n✅ Ready to setup Netlify!', 'green');
  log('Open Netlify dashboard and follow the steps above.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
