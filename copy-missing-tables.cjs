#!/usr/bin/env node

const mysql = require('mysql2/promise');
require('dotenv').config({ path: 'database-copy-config.env' });

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🗄️  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

// Database configurations
const databases = {
  staging: {
    host: 'nozomi.proxy.rlwy.net',
    port: 14321,
    user: 'root',
    password: 'UBdSEpIcUBzNRYPbiyefuuAzEPpmZxTN',
    database: 'railway',
    ssl: { rejectUnauthorized: false }
  },
  source: {
    host: process.env.SOURCE_DB_HOST || 'nozomi.proxy.rlwy.net',
    port: parseInt(process.env.SOURCE_DB_PORT) || 21817,
    user: process.env.SOURCE_DB_USER || 'root',
    password: process.env.SOURCE_DB_PASSWORD || 'pvOcQbzlDAobtcdozbMvCdIDDEmenwkO',
    database: process.env.SOURCE_DB_NAME || 'railway',
    ssl: process.env.SOURCE_DB_SSL === 'true' ? { rejectUnauthorized: false } : { rejectUnauthorized: false }
  }
};

// Create database connection
async function createConnection(config) {
  try {
    const connection = await mysql.createConnection(config);
    log(`✅ Connected to database: ${config.host}:${config.port}/${config.database}`, 'green');
    return connection;
  } catch (error) {
    log(`❌ Failed to connect to database: ${error.message}`, 'red');
    throw error;
  }
}

// Copy specific table
async function copyTable(sourceConnection, targetConnection, tableName) {
  log(`\n📋 Copying table: ${tableName}`, 'blue');
  
  try {
    // Get table structure
    const [createTable] = await sourceConnection.execute(`SHOW CREATE TABLE \`${tableName}\``);
    const createTableSQL = createTable[0]['Create Table'];
    
    // Drop table if exists and recreate
    await targetConnection.execute(`DROP TABLE IF EXISTS \`${tableName}\``);
    await targetConnection.execute(createTableSQL);
    log(`✅ Table structure created: ${tableName}`, 'green');
    
    // Get table data
    const [rows] = await sourceConnection.execute(`SELECT * FROM \`${tableName}\``);
    log(`📊 Found ${rows.length} rows to copy`, 'blue');
    
    if (rows.length > 0) {
      // Get column names
      const columns = Object.keys(rows[0]);
      const columnNames = columns.map(col => `\`${col}\``).join(', ');
      const placeholders = columns.map(() => '?').join(', ');
      
      const insertSQL = `INSERT INTO \`${tableName}\` (${columnNames}) VALUES (${placeholders})`;
      
      // Insert data in batches
      const batchSize = 100;
      let inserted = 0;
      
      for (let i = 0; i < rows.length; i += batchSize) {
        const batch = rows.slice(i, i + batchSize);
        
        for (const row of batch) {
          try {
            const values = columns.map(col => {
              const val = row[col];
              if (val instanceof Date) {
                return val.toISOString().slice(0, 19).replace('T', ' ');
              }
              return val;
            });
            
            await targetConnection.execute(insertSQL, values);
            inserted++;
            
            if (inserted % 50 === 0) {
              log(`Progress: ${inserted}/${rows.length} rows inserted`, 'blue');
            }
          } catch (error) {
            log(`⚠️  Warning: Failed to insert row ${inserted + 1}: ${error.message}`, 'yellow');
          }
        }
      }
      
      log(`✅ Successfully copied ${inserted}/${rows.length} rows`, 'green');
    } else {
      log(`ℹ️  Table ${tableName} is empty`, 'blue');
    }
    
    return true;
  } catch (error) {
    log(`❌ Failed to copy table ${tableName}: ${error.message}`, 'red');
    return false;
  }
}

// Main function
async function main() {
  heading('Copy Missing Tables');
  
  const missingTables = ['pickup_details', 'request_items', 'requests', 'users'];
  
  log('\n📋 Tables to copy:', 'cyan');
  missingTables.forEach(table => log(`  - ${table}`, 'blue'));
  
  try {
    // Connect to both databases
    const sourceConnection = await createConnection(databases.source);
    const targetConnection = await createConnection(databases.staging);
    
    let successCount = 0;
    
    // Copy each missing table
    for (const tableName of missingTables) {
      const success = await copyTable(sourceConnection, targetConnection, tableName);
      if (success) successCount++;
    }
    
    // Close connections
    await sourceConnection.end();
    await targetConnection.end();
    
    log(`\n🎉 Copy completed!`, 'green');
    log(`✅ Successfully copied: ${successCount}/${missingTables.length} tables`, 'green');
    
    if (successCount === missingTables.length) {
      log('\n🚀 All missing tables have been copied successfully!', 'green');
      log('Your staging database now has complete data from production.', 'green');
    } else {
      log(`\n⚠️  ${missingTables.length - successCount} tables failed to copy`, 'yellow');
      log('Please check the errors above and try again if needed.', 'yellow');
    }
    
  } catch (error) {
    log(`\n❌ Operation failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, copyTable };
