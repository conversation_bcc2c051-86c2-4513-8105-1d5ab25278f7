#!/usr/bin/env node

const { execSync } = require('child_process');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('🔗 Getting Staging Environment URLs', 'cyan');
  log('===================================', 'cyan');

  log('\n📋 Railway Staging Information:', 'blue');
  log('Project: gudangmitra-staging', 'blue');
  log('Service: backend-staging', 'blue');
  log('Environment: production (default)', 'blue');

  log('\n🔧 To get the actual backend URL:', 'yellow');
  log('1. Go to Railway dashboard: https://railway.com/project/************************************', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Go to "Settings" tab', 'blue');
  log('4. Look for "Public Domain" or "Generate Domain"', 'blue');
  log('5. Copy the URL (should be like: https://backend-staging-production-xxxx.up.railway.app)', 'blue');

  log('\n🌐 Expected URLs:', 'cyan');
  log('Backend Staging: https://backend-staging-production-[random].up.railway.app', 'blue');
  log('Frontend Staging: https://[site-id]--staging.netlify.app (after Netlify setup)', 'blue');

  log('\n📝 Next Steps:', 'yellow');
  log('1. Get the actual backend URL from Railway dashboard', 'blue');
  log('2. Update .env.staging with the correct URL', 'blue');
  log('3. Setup Netlify staging branch deployment', 'blue');
  log('4. Update Railway CORS_ORIGIN with Netlify staging URL', 'blue');
  log('5. Test both environments', 'blue');

  log('\n🔧 Manual Commands to Run:', 'cyan');
  log('# Update backend URL in frontend config', 'blue');
  log('# Edit .env.staging and replace with actual Railway URL', 'blue');
  log('', 'blue');
  log('# Update CORS in Railway backend', 'blue');
  log('railway variables --set "CORS_ORIGIN=https://[netlify-staging-url]"', 'blue');
  log('', 'blue');
  log('# Test the staging backend', 'blue');
  log('curl https://[backend-staging-url]/health', 'blue');

  log('\n✅ URL information gathering completed!', 'green');
  log('Please complete the manual steps above to finish setup.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
