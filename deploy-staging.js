#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { stdio: 'inherit', encoding: 'utf8' });
    log(`✅ ${description} completed successfully`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

function main() {
  log('🚀 Starting Staging Deployment Process', 'cyan');
  log('=====================================', 'cyan');

  // Check if we're in the right directory
  if (!fs.existsSync('package.json')) {
    log('❌ package.json not found. Please run this script from the project root.', 'red');
    process.exit(1);
  }

  // Check if staging environment files exist
  const requiredFiles = ['.env.staging', 'server/.env.staging', 'railway-staging.toml'];
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log(`⚠️  Warning: ${file} not found. Please create it before deployment.`, 'yellow');
    }
  }

  try {
    // Step 1: Install dependencies
    executeCommand('npm ci', 'Installing dependencies');

    // Step 2: Run linting
    executeCommand('npm run lint', 'Running code linting');

    // Step 3: Build for staging
    executeCommand('npm run build:staging', 'Building application for staging');

    // Step 4: Test staging build locally (optional)
    log('\n📋 Staging build completed successfully!', 'green');
    log('Next steps:', 'bright');
    log('1. Push your changes to the "staging" branch', 'yellow');
    log('2. Netlify will automatically deploy the frontend', 'yellow');
    log('3. Use Railway CLI to deploy backend: railway up --environment staging', 'yellow');
    log('4. Or deploy via Railway dashboard', 'yellow');

    // Step 5: Show environment info
    log('\n🔧 Environment Configuration:', 'cyan');
    log('- Frontend will use: VITE_API_URL from .env.staging', 'blue');
    log('- Backend will use: NODE_ENV=staging', 'blue');
    log('- Database: staging database (separate from production)', 'blue');

    log('\n✅ Staging deployment preparation completed!', 'green');

  } catch (error) {
    log(`❌ Deployment failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the deployment script
if (require.main === module) {
  main();
}

module.exports = { main, executeCommand, log };
