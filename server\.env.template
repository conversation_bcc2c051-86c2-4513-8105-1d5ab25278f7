# Database Configuration Template
# Copy this file to .env.production and .env.staging, then fill with actual values

DB_HOST=your-database-host.railway.app
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=gudang1
DB_PORT=3306
DB_SSL=true

# Server Configuration
PORT=3002
NODE_ENV=production

# CORS Configuration - Frontend URL
CORS_ORIGIN=https://your-frontend-url.netlify.app

# Security
JWT_SECRET=your-jwt-secret-key-here

# Instructions:
# 1. Copy this file to .env.production and .env.staging
# 2. Replace all placeholder values with actual credentials
# 3. For staging: use different database and frontend URLs
# 4. Never commit actual .env files to git
