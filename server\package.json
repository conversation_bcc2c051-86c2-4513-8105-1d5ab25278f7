{"name": "server", "version": "1.0.0", "main": "start.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node railway-server.js", "start:production": "NODE_ENV=production node railway-server.js", "start:staging": "NODE_ENV=staging node railway-server.js", "dev": "nodemon railway-server.js", "dev:staging": "NODE_ENV=staging nodemon railway-server.js", "test:connection": "node -e \"require('./test-db-connection.cjs')\"", "test:env": "node -e \"console.log('Server Environment:', process.env.NODE_ENV || 'development')\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.0", "mysql2": "^3.14.1", "openai": "^5.1.0", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}