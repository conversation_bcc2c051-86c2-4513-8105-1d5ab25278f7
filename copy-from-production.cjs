#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🗄️  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

function main() {
  heading('Copy from Railway Production Database');
  
  log('\n📋 Untuk copy dari Railway production database:', 'blue');
  log('1. Buka Railway dashboard production project', 'blue');
  log('2. Klik pada MySQL database service', 'blue');
  log('3. Copy connection details dari "Connect" tab', 'blue');
  
  log('\n🔧 Update database-copy-config.env dengan:', 'yellow');
  log('', 'blue');
  log('SOURCE_DB_HOST=your-production-host.railway.app', 'blue');
  log('SOURCE_DB_PORT=your-production-port', 'blue');
  log('SOURCE_DB_USER=root', 'blue');
  log('SOURCE_DB_PASSWORD=your-production-password', 'blue');
  log('SOURCE_DB_NAME=railway', 'blue');
  log('SOURCE_DB_SSL=true', 'blue');
  
  log('\n📝 Contoh lengkap:', 'cyan');
  log('SOURCE_DB_HOST=mysql.railway.internal', 'blue');
  log('SOURCE_DB_PORT=3306', 'blue');
  log('SOURCE_DB_USER=root', 'blue');
  log('SOURCE_DB_PASSWORD=AbCdEfGhIjKlMnOpQrStUvWxYz123456', 'blue');
  log('SOURCE_DB_NAME=railway', 'blue');
  log('SOURCE_DB_SSL=true', 'blue');
  
  log('\n🚀 Setelah konfigurasi selesai, jalankan:', 'green');
  log('node test-database-connection.cjs  # Test koneksi', 'blue');
  log('node copy-database.cjs copy source staging  # Copy database', 'blue');
  
  log('\n⚠️  Catatan penting:', 'red');
  log('- Pastikan production database dapat diakses dari luar', 'red');
  log('- Copy akan mengganti semua data di staging database', 'red');
  log('- Proses copy mungkin memakan waktu tergantung ukuran data', 'red');
}

if (require.main === module) {
  main();
}

module.exports = { main };
