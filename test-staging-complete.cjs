#!/usr/bin/env node

const https = require('https');
const http = require('http');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testEndpoint(url, name) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          log(`✅ ${name}: OK (${res.statusCode})`, 'green');
          resolve({ success: true, status: res.statusCode, data });
        } else {
          log(`⚠️  ${name}: Warning (${res.statusCode})`, 'yellow');
          resolve({ success: false, status: res.statusCode, data });
        }
      });
    });

    req.on('error', (error) => {
      log(`❌ ${name}: Failed - ${error.message}`, 'red');
      resolve({ success: false, error: error.message });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      log(`⏰ ${name}: Timeout`, 'yellow');
      resolve({ success: false, error: 'Timeout' });
    });
  });
}

async function main() {
  log('🧪 Testing Complete Staging Environment', 'cyan');
  log('======================================', 'cyan');

  log('\n📋 Testing Checklist:', 'blue');
  log('This script will test both production and staging environments', 'blue');

  const endpoints = [
    {
      url: 'https://gudangmitra-production.up.railway.app/health',
      name: 'Production Backend Health',
      critical: false
    },
    {
      url: 'https://gudangmitra-production.up.railway.app/api/test-connection',
      name: 'Production Database Connection',
      critical: false
    },
    {
      url: 'https://gudang.netlify.app',
      name: 'Production Frontend',
      critical: false
    }
  ];

  // Add staging endpoints (these will need to be updated with actual URLs)
  log('\n⚠️  Please update these URLs with your actual staging URLs:', 'yellow');
  log('1. Replace [backend-staging-url] with your Railway backend URL', 'blue');
  log('2. Replace [netlify-staging-url] with your Netlify staging URL', 'blue');

  const stagingEndpoints = [
    {
      url: 'https://[backend-staging-url]/health',
      name: 'Staging Backend Health',
      critical: true
    },
    {
      url: 'https://[backend-staging-url]/api/test-connection',
      name: 'Staging Database Connection',
      critical: true
    },
    {
      url: 'https://[netlify-staging-url]',
      name: 'Staging Frontend',
      critical: true
    }
  ];

  log('\n🔍 Testing Production Environment:', 'cyan');
  const productionResults = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint.url, endpoint.name);
    productionResults.push({ ...endpoint, ...result });
  }

  log('\n🔍 Staging Environment URLs to Test:', 'cyan');
  stagingEndpoints.forEach(endpoint => {
    log(`${endpoint.name}: ${endpoint.url}`, 'blue');
  });

  log('\n📊 Production Test Summary:', 'cyan');
  const productionSuccessful = productionResults.filter(r => r.success).length;
  const productionTotal = productionResults.length;
  
  log(`✅ Successful: ${productionSuccessful}/${productionTotal}`, 
      productionSuccessful === productionTotal ? 'green' : 'yellow');

  if (productionSuccessful < productionTotal) {
    log('\n⚠️  Failed production endpoints:', 'yellow');
    productionResults.filter(r => !r.success).forEach(r => {
      log(`   - ${r.name}: ${r.error || `HTTP ${r.status}`}`, 'red');
    });
  }

  log('\n📋 Manual Testing Steps for Staging:', 'yellow');
  log('1. Get your actual backend staging URL from Railway dashboard', 'blue');
  log('2. Get your actual frontend staging URL from Netlify', 'blue');
  log('3. Test backend health: curl https://[backend-staging-url]/health', 'blue');
  log('4. Test database: curl https://[backend-staging-url]/api/test-connection', 'blue');
  log('5. Test frontend: open https://[netlify-staging-url] in browser', 'blue');
  log('6. Test login functionality in staging frontend', 'blue');
  log('7. Test API calls from staging frontend to staging backend', 'blue');

  log('\n🔧 Common Issues & Solutions:', 'cyan');
  log('❌ CORS Error:', 'red');
  log('   → Update CORS_ORIGIN in Railway backend variables', 'blue');
  log('   → railway variables --set "CORS_ORIGIN=https://[netlify-staging-url]"', 'blue');
  log('', 'blue');
  log('❌ Database Connection Error:', 'red');
  log('   → Check database credentials in Railway backend variables', 'blue');
  log('   → Verify MySQL service is running', 'blue');
  log('', 'blue');
  log('❌ Frontend Build Error:', 'red');
  log('   → Check .env.staging has correct backend URL', 'blue');
  log('   → Verify netlify.toml configuration', 'blue');

  log('\n🎯 Success Criteria:', 'green');
  log('✅ Backend staging health endpoint returns 200', 'blue');
  log('✅ Backend staging database connection works', 'blue');
  log('✅ Frontend staging loads without errors', 'blue');
  log('✅ Frontend staging can call backend staging APIs', 'blue');
  log('✅ No CORS errors in browser console', 'blue');

  log('\n📚 Useful Commands:', 'cyan');
  log('# Check Railway backend logs:', 'blue');
  log('railway logs', 'blue');
  log('', 'blue');
  log('# Check Railway variables:', 'blue');
  log('railway variables', 'blue');
  log('', 'blue');
  log('# Update CORS after getting Netlify URL:', 'blue');
  log('railway variables --set "CORS_ORIGIN=https://[netlify-staging-url]"', 'blue');
  log('', 'blue');
  log('# Test local staging build:', 'blue');
  log('npm run build:staging', 'blue');
  log('npm run preview:staging', 'blue');

  log('\n✅ Testing script completed!', 'green');
  log('Please complete the manual testing steps above.', 'yellow');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, testEndpoint };
