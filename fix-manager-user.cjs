const mysql = require('mysql2/promise');

async function fixManagerUser() {
  try {
    console.log('Connecting to Railway database...');
    const connection = await mysql.createConnection({
      host: 'junction.proxy.rlwy.net',
      port: 47292,
      user: 'root',
      password: 'QLOJOCJCJjJGgJJJJJJJJJJJJJJJJJJJ',
      database: 'railway'
    });

    console.log('Connected to Railway database');
    
    // Check users <NAME_EMAIL>
    const [users] = await connection.query(
      'SELECT id, name, email, role FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    console.log(`Found ${users.length} users <NAME_EMAIL>`);
    console.log('Users:', users);
    
    // Update user ID 1 to have different email
    const [result] = await connection.query(
      'UPDATE users SET email = ? WHERE id = ?',
      ['<EMAIL>', 1]
    );
    
    console.log(`Updated user ID 1 <NAME_EMAIL>`);
    console.log(`Affected rows: ${result.affectedRows}`);
    
    // Check users again
    const [updatedUsers] = await connection.query(
      'SELECT id, name, email, role FROM users WHERE id IN (1, 3)'
    );
    
    console.log('Updated users:');
    updatedUsers.forEach(user => {
      console.log(`- ID: ${user.id}, Name: ${user.name}, Email: ${user.email}, Role: ${user.role}`);
    });
    
    await connection.end();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

fixManagerUser();
