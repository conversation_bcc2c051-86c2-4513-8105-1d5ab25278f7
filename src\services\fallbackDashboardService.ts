import { API_BASE_URL } from '../config';

const API_URL = API_BASE_URL;
import { DashboardStats } from './dashboardService';

/**
 * Fallback dashboard service that calculates stats from existing endpoints
 * when dedicated dashboard endpoints are not available
 */
class FallbackDashboardService {
  /**
   * Get dashboard statistics by calling individual endpoints
   */
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      console.log("Fetching dashboard statistics using fallback method...");
      
      // Fetch data from existing endpoints in parallel
      const [usersData, itemsData, requestsData] = await Promise.all([
        this.fetchUsers(),
        this.fetchItems(),
        this.fetchRequests()
      ]);

      // Calculate user statistics
      const userStats = this.calculateUserStats(usersData);
      
      // Calculate item statistics
      const itemStats = this.calculateItemStats(itemsData);
      
      // Calculate request statistics
      const requestStats = this.calculateRequestStats(requestsData);
      
      // Calculate top requested items
      const topRequestedItems = this.calculateTopRequestedItems(requestsData, itemsData);
      
      // Calculate recent activity
      const recentActivity = this.calculateRecentActivity(requestsData);

      const dashboardStats: DashboardStats = {
        // User statistics
        totalUsers: userStats.totalUsers,
        usersByRole: userStats.usersByRole,

        // Item statistics
        totalItems: itemStats.totalItems,
        totalQuantity: itemStats.totalQuantity,
        lowStockItems: itemStats.lowStockItems,
        totalCategories: itemStats.totalCategories,

        // Request statistics
        totalRequests: requestStats.totalRequests,
        requestsByStatus: requestStats.requestsByStatus,
        recentRequests: requestStats.recentRequests,

        // Top requested items
        topRequestedItems,

        // Recent activity
        recentActivity
      };

      console.log("Fallback dashboard stats calculated successfully:", dashboardStats);
      return dashboardStats;
      
    } catch (error) {
      console.error("Error fetching fallback dashboard stats:", error);
      return this.getDefaultStats();
    }
  }

  private async fetchUsers(): Promise<any[]> {
    try {
      const response = await fetch(`${API_URL}/users`);
      if (!response.ok) throw new Error(`Users API error: ${response.status}`);
      const data = await response.json();
      return data.success ? data.data : [];
    } catch (error) {
      console.error("Error fetching users:", error);
      return [];
    }
  }

  private async fetchItems(): Promise<any[]> {
    try {
      const response = await fetch(`${API_URL}/items`);
      if (!response.ok) throw new Error(`Items API error: ${response.status}`);
      const data = await response.json();
      return data.success ? data.data : [];
    } catch (error) {
      console.error("Error fetching items:", error);
      return [];
    }
  }

  private async fetchRequests(): Promise<any[]> {
    try {
      const response = await fetch(`${API_URL}/requests`);
      if (!response.ok) throw new Error(`Requests API error: ${response.status}`);
      const data = await response.json();
      return data.success ? data.data : [];
    } catch (error) {
      console.error("Error fetching requests:", error);
      return [];
    }
  }

  private calculateUserStats(users: any[]) {
    const totalUsers = users.length;
    const usersByRole = users.reduce((acc, user) => {
      const role = user.role || 'user';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, { admin: 0, manager: 0, user: 0 });

    return { totalUsers, usersByRole };
  }

  private calculateItemStats(items: any[]) {
    const totalItems = items.length;
    const totalQuantity = items.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
    const lowStockItems = items.filter(item => 
      parseInt(item.quantity) <= parseInt(item.minQuantity || 0)
    ).length;
    
    const categories = new Set(items.map(item => item.category).filter(Boolean));
    const totalCategories = categories.size;

    return { totalItems, totalQuantity, lowStockItems, totalCategories };
  }

  private calculateRequestStats(requests: any[]) {
    const totalRequests = requests.length;
    
    const requestsByStatus = requests.reduce((acc, request) => {
      const status = request.status || 'pending';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, { pending: 0, approved: 0, denied: 0, fulfilled: 0 });

    // Calculate recent requests (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentRequests = requests.filter(request => {
      const createdAt = new Date(request.created_at || request.createdAt);
      return createdAt >= sevenDaysAgo;
    }).length;

    return { totalRequests, requestsByStatus, recentRequests };
  }

  private calculateTopRequestedItems(requests: any[], items: any[]) {
    // Create a map of item requests
    const itemRequestCounts: { [key: string]: number } = {};
    
    requests.forEach(request => {
      if (request.items && Array.isArray(request.items)) {
        request.items.forEach((item: any) => {
          const itemName = item.name || 'Unknown Item';
          const quantity = parseInt(item.quantity) || 1;
          itemRequestCounts[itemName] = (itemRequestCounts[itemName] || 0) + quantity;
        });
      } else if (request.item_name) {
        const quantity = parseInt(request.quantity) || 1;
        itemRequestCounts[request.item_name] = (itemRequestCounts[request.item_name] || 0) + quantity;
      }
    });

    // Convert to array and sort
    const topItems = Object.entries(itemRequestCounts)
      .map(([name, totalRequested]) => ({ name, totalRequested }))
      .sort((a, b) => b.totalRequested - a.totalRequested)
      .slice(0, 5);

    return topItems;
  }

  private calculateRecentActivity(requests: any[]) {
    return requests
      .sort((a, b) => {
        const dateA = new Date(a.created_at || a.createdAt);
        const dateB = new Date(b.created_at || b.createdAt);
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 10)
      .map(request => ({
        id: request.id?.toString() || 'unknown',
        type: 'request_created' as const,
        description: `Request #${request.id} created by ${request.requester_name || 'Unknown User'}`,
        timestamp: new Date(request.created_at || request.createdAt).toISOString(),
        user: request.requester_name || 'Unknown User'
      }));
  }

  private getDefaultStats(): DashboardStats {
    return {
      totalUsers: 0,
      usersByRole: { admin: 0, manager: 0, user: 0 },
      totalItems: 0,
      totalQuantity: 0,
      lowStockItems: 0,
      totalCategories: 0,
      totalRequests: 0,
      requestsByStatus: { pending: 0, approved: 0, denied: 0, fulfilled: 0 },
      recentRequests: 0,
      topRequestedItems: [],
      recentActivity: []
    };
  }
}

export const fallbackDashboardService = new FallbackDashboardService();
