#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('🔗 Getting Backend Staging URL', 'cyan');
  log('==============================', 'cyan');

  log('\n📋 Steps to Get Backend URL:', 'yellow');
  log('1. Go to Railway dashboard: https://railway.com/project/************************************', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Go to "Settings" tab', 'blue');
  log('4. Look for "Networking" section', 'blue');
  log('5. Click "Generate Domain" if no domain exists', 'blue');
  log('6. Copy the generated URL (format: https://backend-staging-production-xxxx.up.railway.app)', 'blue');

  log('\n📝 After getting the URL, update these files:', 'yellow');
  
  // Show current .env.staging content
  if (fs.existsSync('.env.staging')) {
    log('\nCurrent .env.staging:', 'blue');
    const envContent = fs.readFileSync('.env.staging', 'utf8');
    console.log(envContent);
    
    log('\n🔧 Update the VITE_API_URL line to:', 'yellow');
    log('VITE_API_URL=https://[your-backend-staging-url]/api', 'blue');
  }

  log('\n⚡ Quick Commands After Getting URL:', 'cyan');
  log('# Test the backend health endpoint:', 'blue');
  log('curl https://[your-backend-url]/health', 'blue');
  log('', 'blue');
  log('# Test database connection:', 'blue');
  log('curl https://[your-backend-url]/api/test-connection', 'blue');
  log('', 'blue');
  log('# If URLs work, proceed to Netlify setup:', 'blue');
  log('node setup-netlify-branch.cjs', 'blue');

  log('\n💡 Expected Backend URL Format:', 'cyan');
  log('https://backend-staging-production-[random-string].up.railway.app', 'blue');

  log('\n📋 Checklist:', 'yellow');
  log('□ Database credentials set in Railway backend-staging service', 'blue');
  log('□ Backend domain generated in Railway', 'blue');
  log('□ .env.staging updated with correct backend URL', 'blue');
  log('□ Backend health endpoint responding', 'blue');

  log('\n✅ After completing this step, run: node setup-netlify-branch.cjs', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { main };
