#!/usr/bin/env node

const { execSync } = require('child_process');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

function main() {
  log('🚀 Push Staging Branch to Repository', 'cyan');
  log('====================================', 'cyan');

  // Check current branch
  const currentBranch = executeCommand('git branch --show-current', 'Checking current branch');
  log(`Current branch: ${currentBranch?.trim()}`, 'blue');

  // Check if we have a remote
  const remotes = executeCommand('git remote -v', 'Checking git remotes');
  
  if (!remotes || remotes.trim() === '') {
    log('\n⚠️  No Git remote found!', 'yellow');
    log('You need to set up a Git repository first.', 'yellow');
    
    log('\n📋 Options to set up Git remote:', 'cyan');
    log('1. Create a new GitHub repository:', 'blue');
    log('   - Go to https://github.com/new', 'blue');
    log('   - Create repository "gudangsub-staging"', 'blue');
    log('   - Copy the repository URL', 'blue');
    log('', 'blue');
    log('2. Add the remote:', 'blue');
    log('   git remote add origin https://github.com/[username]/gudangsub-staging.git', 'blue');
    log('', 'blue');
    log('3. Push both branches:', 'blue');
    log('   git push -u origin main', 'blue');
    log('   git push -u origin staging', 'blue');

    log('\n🔄 Alternative: Use existing repository', 'cyan');
    log('If you already have a repository for this project:', 'blue');
    log('1. Add the remote:', 'blue');
    log('   git remote add origin [your-repo-url]', 'blue');
    log('2. Push staging branch:', 'blue');
    log('   git push origin staging', 'blue');

    return;
  }

  log('\n📋 Git remotes found:', 'blue');
  console.log(remotes);

  // Check git status
  const status = executeCommand('git status --porcelain', 'Checking git status');
  
  if (status && status.trim() !== '') {
    log('\n📝 Uncommitted changes found. Committing...', 'yellow');
    executeCommand('git add .', 'Adding all changes');
    executeCommand('git commit -m "Final staging setup - ready for deployment"', 'Committing changes');
  }

  // Try to push staging branch
  log('\n🚀 Pushing staging branch...', 'cyan');
  const pushResult = executeCommand('git push origin staging', 'Pushing staging branch to remote');
  
  if (pushResult !== null) {
    log('\n🎉 SUCCESS! Staging branch pushed successfully!', 'green');
    
    log('\n📋 Next Steps:', 'cyan');
    log('1. Go to your Netlify dashboard: https://app.netlify.com/', 'blue');
    log('2. Select your site', 'blue');
    log('3. Enable branch deploys for "staging" branch', 'blue');
    log('4. Netlify will automatically deploy staging branch', 'blue');
    log('5. Get the staging URL from Netlify', 'blue');
    log('6. Update CORS_ORIGIN in Railway with the staging URL', 'blue');

    log('\n🔗 Useful Links:', 'cyan');
    log('- Railway Project: https://railway.com/project/************************************', 'blue');
    log('- Netlify Dashboard: https://app.netlify.com/', 'blue');

    log('\n✅ Ready for Netlify setup!', 'green');
  } else {
    log('\n❌ Failed to push staging branch', 'red');
    log('Please check your Git remote configuration and try again.', 'yellow');
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
