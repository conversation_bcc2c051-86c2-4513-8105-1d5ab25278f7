#!/usr/bin/env node

const mysql = require('mysql2/promise');
require('dotenv').config({ path: 'server/.env.staging' });

async function testConnection() {
  try {
    console.log('🔍 Testing Railway database connection...');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT,
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
    });
    
    await connection.execute('SELECT 1 as test');
    await connection.end();
    
    console.log('✅ Database connection successful!');
    console.log('🎉 Railway staging database is ready!');
  } catch (error) {
    console.log('❌ Database connection failed:');
    console.log(error.message);
    console.log('\n💡 Please check your environment variables in server/.env.staging');
  }
}

testConnection();
