#!/usr/bin/env node

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: 'database-copy-config.env' });

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🗄️  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

// Database configurations
const databases = {
  staging: {
    host: 'nozomi.proxy.rlwy.net',
    port: 14321,
    user: 'root',
    password: 'UBdSEpIcUBzNRYPbiyefuuAzEPpmZxTN',
    database: 'railway',
    ssl: { rejectUnauthorized: false }
  },
  // Add your source database config here
  source: {
    // Example for local database
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'your_password',
    database: 'gudang_mitra'
  }
};

// Get database configuration from environment or use defaults
function getDatabaseConfig(env) {
  if (env === 'staging') {
    return databases.staging;
  }
  
  // For source database, try to get from environment variables
  return {
    host: process.env.SOURCE_DB_HOST || 'localhost',
    port: process.env.SOURCE_DB_PORT || 3306,
    user: process.env.SOURCE_DB_USER || 'root',
    password: process.env.SOURCE_DB_PASSWORD || '',
    database: process.env.SOURCE_DB_NAME || 'gudang_mitra',
    ssl: process.env.SOURCE_DB_SSL === 'true' ? { rejectUnauthorized: false } : false
  };
}

// Create database connection
async function createConnection(config) {
  try {
    const connection = await mysql.createConnection(config);
    log(`✅ Connected to database: ${config.host}:${config.port}/${config.database}`, 'green');
    return connection;
  } catch (error) {
    log(`❌ Failed to connect to database: ${error.message}`, 'red');
    throw error;
  }
}

// Export database schema and data
async function exportDatabase(connection, outputFile) {
  log('\n📤 Exporting database...', 'blue');
  
  try {
    // Get all tables
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    
    log(`Found ${tableNames.length} tables: ${tableNames.join(', ')}`, 'blue');
    
    let sqlDump = '';
    sqlDump += '-- Database Export\n';
    sqlDump += `-- Generated on: ${new Date().toISOString()}\n\n`;
    sqlDump += 'SET FOREIGN_KEY_CHECKS = 0;\n\n';
    
    for (const tableName of tableNames) {
      log(`Exporting table: ${tableName}`, 'blue');
      
      // Get table structure
      const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
      sqlDump += `-- Table: ${tableName}\n`;
      sqlDump += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      sqlDump += createTable[0]['Create Table'] + ';\n\n';
      
      // Get table data
      const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
      
      if (rows.length > 0) {
        sqlDump += `-- Data for table: ${tableName}\n`;
        
        for (const row of rows) {
          const columns = Object.keys(row).map(col => `\`${col}\``).join(', ');
          const values = Object.values(row).map(val => {
            if (val === null) return 'NULL';
            if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`;
            if (val instanceof Date) return `'${val.toISOString().slice(0, 19).replace('T', ' ')}'`;
            return val;
          }).join(', ');
          
          sqlDump += `INSERT INTO \`${tableName}\` (${columns}) VALUES (${values});\n`;
        }
        sqlDump += '\n';
      }
    }
    
    sqlDump += 'SET FOREIGN_KEY_CHECKS = 1;\n';
    
    // Write to file
    fs.writeFileSync(outputFile, sqlDump);
    log(`✅ Database exported to: ${outputFile}`, 'green');
    log(`📊 Export size: ${(fs.statSync(outputFile).size / 1024).toFixed(2)} KB`, 'green');
    
    return outputFile;
  } catch (error) {
    log(`❌ Export failed: ${error.message}`, 'red');
    throw error;
  }
}

// Import database from SQL file
async function importDatabase(connection, sqlFile) {
  log('\n📥 Importing database...', 'blue');
  
  try {
    if (!fs.existsSync(sqlFile)) {
      throw new Error(`SQL file not found: ${sqlFile}`);
    }
    
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    log(`Executing ${statements.length} SQL statements...`, 'blue');
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement.length > 0 && !statement.startsWith('--')) {
        try {
          await connection.execute(statement);
          if (i % 10 === 0) {
            log(`Progress: ${i + 1}/${statements.length} statements`, 'blue');
          }
        } catch (error) {
          log(`⚠️  Warning: Failed to execute statement ${i + 1}: ${error.message}`, 'yellow');
        }
      }
    }
    
    log(`✅ Database imported successfully`, 'green');
  } catch (error) {
    log(`❌ Import failed: ${error.message}`, 'red');
    throw error;
  }
}

// Main function
async function main() {
  heading('Database Copy Tool');
  
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command || !['export', 'import', 'copy'].includes(command)) {
    log('Usage:', 'yellow');
    log('  node copy-database.cjs export [source_env] [output_file]', 'blue');
    log('  node copy-database.cjs import [target_env] [sql_file]', 'blue');
    log('  node copy-database.cjs copy [source_env] [target_env]', 'blue');
    log('', 'blue');
    log('Examples:', 'yellow');
    log('  node copy-database.cjs export source database_backup.sql', 'blue');
    log('  node copy-database.cjs import staging database_backup.sql', 'blue');
    log('  node copy-database.cjs copy source staging', 'blue');
    return;
  }
  
  try {
    if (command === 'export') {
      const sourceEnv = args[1] || 'source';
      const outputFile = args[2] || `database_export_${Date.now()}.sql`;
      
      log(`Exporting from: ${sourceEnv}`, 'cyan');
      log(`Output file: ${outputFile}`, 'cyan');
      
      const sourceConfig = getDatabaseConfig(sourceEnv);
      const sourceConnection = await createConnection(sourceConfig);
      
      await exportDatabase(sourceConnection, outputFile);
      await sourceConnection.end();
      
    } else if (command === 'import') {
      const targetEnv = args[1] || 'staging';
      const sqlFile = args[2];
      
      if (!sqlFile) {
        log('❌ SQL file is required for import', 'red');
        return;
      }
      
      log(`Importing to: ${targetEnv}`, 'cyan');
      log(`SQL file: ${sqlFile}`, 'cyan');
      
      const targetConfig = getDatabaseConfig(targetEnv);
      const targetConnection = await createConnection(targetConfig);
      
      await importDatabase(targetConnection, sqlFile);
      await targetConnection.end();
      
    } else if (command === 'copy') {
      const sourceEnv = args[1] || 'source';
      const targetEnv = args[2] || 'staging';
      const tempFile = `temp_export_${Date.now()}.sql`;
      
      log(`Copying from: ${sourceEnv} to ${targetEnv}`, 'cyan');
      
      // Export from source
      const sourceConfig = getDatabaseConfig(sourceEnv);
      const sourceConnection = await createConnection(sourceConfig);
      await exportDatabase(sourceConnection, tempFile);
      await sourceConnection.end();
      
      // Import to target
      const targetConfig = getDatabaseConfig(targetEnv);
      const targetConnection = await createConnection(targetConfig);
      await importDatabase(targetConnection, tempFile);
      await targetConnection.end();
      
      // Clean up temp file
      fs.unlinkSync(tempFile);
      log(`🗑️  Cleaned up temporary file: ${tempFile}`, 'blue');
    }
    
    log('\n🎉 Database operation completed successfully!', 'green');
    
  } catch (error) {
    log(`\n❌ Operation failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, exportDatabase, importDatabase };
