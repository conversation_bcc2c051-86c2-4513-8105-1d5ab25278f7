const express = require("express");
const cors = require("cors");
const mysql = require("mysql2/promise");

// Load environment variables
// In Railway, environment variables are set via dashboard, no .env file needed
if (process.env.NODE_ENV !== 'production' && !process.env.RAILWAY_ENVIRONMENT) {
  const envFile = `.env.${process.env.NODE_ENV || 'development'}`;
  console.log(`🔧 Loading environment from: ${envFile}`);
  require('dotenv').config({ path: envFile });
} else {
  console.log(`🔧 Using Railway environment variables`);
}

console.log('🚀 Starting Simple Railway Server...');
console.log('Environment variables:');
console.log('PORT:', process.env.PORT);
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST);

const app = express();
const PORT = process.env.PORT || 3000;

// CORS configuration
const allowedOrigins = [
  'https://gudang.netlify.app',
  'https://gudang-staging.netlify.app',
  'https://gudang-mitra-staging.netlify.app',
  'http://localhost:5173',
  'http://localhost:3000'
];

if (process.env.CORS_ORIGIN) {
  allowedOrigins.push(process.env.CORS_ORIGIN);
}

app.use(cors({
  origin: allowedOrigins,
  credentials: true
}));

app.use(express.json());

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306', 10),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false
};

let pool;

// Initialize database pool
try {
  pool = mysql.createPool(dbConfig);
  console.log('✅ Database pool created');
} catch (error) {
  console.error('❌ Failed to create database pool:', error);
}

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Railway Staging Server is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'production'
  });
});

// API health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is healthy',
    timestamp: new Date().toISOString(),
    database: pool ? 'connected' : 'disconnected'
  });
});

// Test database connection
app.get('/api/test-connection', async (req, res) => {
  try {
    if (!pool) {
      return res.status(500).json({
        success: false,
        message: 'Database pool not initialized'
      });
    }

    const [rows] = await pool.execute('SELECT 1 as test');
    res.json({
      success: true,
      message: 'Database connection successful',
      result: rows[0]
    });
  } catch (error) {
    console.error('Database connection test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Get all users
app.get('/api/users', async (req, res) => {
  try {
    if (!pool) {
      return res.status(500).json({
        success: false,
        message: 'Database pool not initialized'
      });
    }

    const [rows] = await pool.execute('SELECT id, name, email, role FROM users');
    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
});

// Get all items
app.get('/api/items', async (req, res) => {
  try {
    if (!pool) {
      return res.status(500).json({
        success: false,
        message: 'Database pool not initialized'
      });
    }

    const [rows] = await pool.execute('SELECT * FROM items');
    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('Error fetching items:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch items',
      error: error.message
    });
  }
});

// Get all requests
app.get('/api/requests', async (req, res) => {
  try {
    if (!pool) {
      return res.status(500).json({
        success: false,
        message: 'Database pool not initialized'
      });
    }

    const [rows] = await pool.execute('SELECT * FROM requests');
    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('Error fetching requests:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch requests',
      error: error.message
    });
  }
});

// Login endpoint (simplified)
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // For staging, return mock success
    res.json({
      success: true,
      message: 'Login successful (staging mode)',
      user: {
        id: 1,
        name: 'Test User',
        email: email,
        role: 'admin'
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.path} not found`
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: error.message
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'production'}`);
  console.log(`🔗 Health check: http://0.0.0.0:${PORT}/`);
  console.log(`🔗 API health: http://0.0.0.0:${PORT}/api/health`);
  console.log(`🌍 Railway PORT: ${process.env.PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  if (pool) {
    await pool.end();
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  if (pool) {
    await pool.end();
  }
  process.exit(0);
});
