# Database Copy Configuration Template
# Copy this file to database-copy-config.env and fill with your source database details

# Source Database Configuration (Production Database)
SOURCE_DB_HOST=nozomi.proxy.rlwy.net
SOURCE_DB_PORT=21817
SOURCE_DB_USER=root
SOURCE_DB_PASSWORD=pvOcQbzlDAobtcdozbMvCdIDDEmenwkO
SOURCE_DB_NAME=railway
SOURCE_DB_SSL=true

# Alternative: Railway Production Database
# SOURCE_DB_HOST=your-production-railway-host
# SOURCE_DB_PORT=your-production-railway-port
# SOURCE_DB_USER=root
# SOURCE_DB_PASSWORD=your-production-railway-password
# SOURCE_DB_NAME=railway
# SOURCE_DB_SSL=true

# Instructions:
# 1. Copy this file to database-copy-config.env
# 2. Fill in your source database details
# 3. Run: node copy-database.cjs copy source staging
