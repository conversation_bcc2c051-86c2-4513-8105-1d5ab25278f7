# Database Copy Configuration Template
# Copy this file to database-copy-config.env and fill with your source database details

# Source Database Configuration (Database yang ingin di-copy)
SOURCE_DB_HOST=localhost
SOURCE_DB_PORT=3306
SOURCE_DB_USER=root
SOURCE_DB_PASSWORD=your_password_here
SOURCE_DB_NAME=gudang_mitra
SOURCE_DB_SSL=false

# Alternative: Railway Production Database
# SOURCE_DB_HOST=your-production-railway-host
# SOURCE_DB_PORT=your-production-railway-port
# SOURCE_DB_USER=root
# SOURCE_DB_PASSWORD=your-production-railway-password
# SOURCE_DB_NAME=railway
# SOURCE_DB_SSL=true

# Instructions:
# 1. Copy this file to database-copy-config.env
# 2. Fill in your source database details
# 3. Run: node copy-database.cjs copy source staging
