#!/usr/bin/env node

const mysql = require('mysql2/promise');
const fs = require('fs');
require('dotenv').config({ path: 'database-copy-config.env' });

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🔍  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

// Database configurations
const databases = {
  staging: {
    host: 'nozomi.proxy.rlwy.net',
    port: 14321,
    user: 'root',
    password: 'UBdSEpIcUBzNRYPbiyefuuAzEPpmZxTN',
    database: 'railway',
    ssl: { rejectUnauthorized: false }
  },
  source: {
    host: process.env.SOURCE_DB_HOST || 'localhost',
    port: parseInt(process.env.SOURCE_DB_PORT) || 3306,
    user: process.env.SOURCE_DB_USER || 'root',
    password: process.env.SOURCE_DB_PASSWORD || '',
    database: process.env.SOURCE_DB_NAME || 'gudang_mitra',
    ssl: process.env.SOURCE_DB_SSL === 'true' ? { rejectUnauthorized: false } : false
  }
};

// Test database connection
async function testConnection(name, config) {
  log(`\n🔌 Testing ${name} database connection...`, 'blue');
  log(`Host: ${config.host}:${config.port}`, 'blue');
  log(`Database: ${config.database}`, 'blue');
  log(`User: ${config.user}`, 'blue');
  log(`SSL: ${config.ssl ? 'enabled' : 'disabled'}`, 'blue');
  
  try {
    const connection = await mysql.createConnection(config);
    
    // Test basic query
    const [result] = await connection.execute('SELECT 1 as test');
    log(`✅ ${name} connection successful!`, 'green');
    
    // Get database info
    const [tables] = await connection.execute('SHOW TABLES');
    log(`📊 Found ${tables.length} tables`, 'green');
    
    if (tables.length > 0) {
      log(`Tables: ${tables.map(row => Object.values(row)[0]).join(', ')}`, 'blue');
      
      // Get row counts for each table
      for (const tableRow of tables.slice(0, 5)) { // Limit to first 5 tables
        const tableName = Object.values(tableRow)[0];
        try {
          const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM \`${tableName}\``);
          const count = countResult[0].count;
          log(`  - ${tableName}: ${count} rows`, 'blue');
        } catch (error) {
          log(`  - ${tableName}: Error getting count`, 'yellow');
        }
      }
      
      if (tables.length > 5) {
        log(`  ... and ${tables.length - 5} more tables`, 'blue');
      }
    }
    
    await connection.end();
    return true;
    
  } catch (error) {
    log(`❌ ${name} connection failed: ${error.message}`, 'red');
    
    if (error.code === 'ENOTFOUND') {
      log(`💡 Tip: Check if the host address is correct`, 'yellow');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      log(`💡 Tip: Check username and password`, 'yellow');
    } else if (error.code === 'ECONNREFUSED') {
      log(`💡 Tip: Check if the database server is running and port is correct`, 'yellow');
    }
    
    return false;
  }
}

async function main() {
  heading('Database Connection Test');
  
  // Check if config file exists
  if (!fs.existsSync('database-copy-config.env')) {
    log('⚠️  Configuration file not found!', 'yellow');
    log('Please create database-copy-config.env from the template:', 'yellow');
    log('cp database-copy-config.env.template database-copy-config.env', 'blue');
    log('Then edit the file with your source database details.', 'blue');
    log('\nTesting staging database only...', 'yellow');
    
    // Test staging only
    const stagingSuccess = await testConnection('Staging', databases.staging);
    
    if (stagingSuccess) {
      log('\n✅ Staging database is ready!', 'green');
      log('Create database-copy-config.env to test source database.', 'yellow');
    }
    
    return;
  }
  
  log('📁 Found database-copy-config.env', 'green');
  
  // Test both databases
  const stagingSuccess = await testConnection('Staging', databases.staging);
  const sourceSuccess = await testConnection('Source', databases.source);
  
  log('\n📋 Connection Test Summary:', 'cyan');
  log(`Staging Database: ${stagingSuccess ? '✅ Connected' : '❌ Failed'}`, stagingSuccess ? 'green' : 'red');
  log(`Source Database: ${sourceSuccess ? '✅ Connected' : '❌ Failed'}`, sourceSuccess ? 'green' : 'red');
  
  if (stagingSuccess && sourceSuccess) {
    log('\n🎉 Both databases are accessible!', 'green');
    log('You can now copy the database:', 'green');
    log('node copy-database.cjs copy source staging', 'blue');
  } else if (stagingSuccess) {
    log('\n⚠️  Only staging database is accessible.', 'yellow');
    log('Please check your source database configuration.', 'yellow');
  } else if (sourceSuccess) {
    log('\n⚠️  Only source database is accessible.', 'yellow');
    log('Staging database connection failed - this should not happen.', 'red');
  } else {
    log('\n❌ Both database connections failed.', 'red');
    log('Please check your configurations and network connectivity.', 'red');
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, testConnection };
