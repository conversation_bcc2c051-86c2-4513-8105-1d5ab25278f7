#!/usr/bin/env node

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('🎯 MANUAL SETUP GUIDE - Step by Step', 'cyan');
  log('====================================', 'cyan');

  log('\n🚨 Railway CLI seems to have connection issues.', 'yellow');
  log('Let\'s complete the setup using Railway Dashboard (Web UI)', 'yellow');

  log('\n📋 STEP 1: Database Setup (5 minutes)', 'cyan');
  log('======================================', 'cyan');
  
  log('\n🌐 1.1 Open Railway Dashboard:', 'yellow');
  log('https://railway.com/project/************************************', 'blue');
  
  log('\n🗄️  1.2 Get MySQL Database Credentials:', 'yellow');
  log('1. Click on "MySQL" service (database icon)', 'blue');
  log('2. Click "Variables" tab', 'blue');
  log('3. Copy these values (write them down):', 'blue');
  log('   📝 MYSQL_HOST: ________________', 'blue');
  log('   📝 MYSQL_USER: ________________', 'blue');
  log('   📝 MYSQL_PASSWORD: ________________', 'blue');
  log('   📝 MYSQL_DATABASE: ________________', 'blue');
  log('   📝 MYSQL_PORT: 3306 (usually)', 'blue');

  log('\n⚙️  1.3 Set Variables in Backend Service:', 'yellow');
  log('1. Go back to project overview', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Click "Variables" tab', 'blue');
  log('4. Click "New Variable" and add these one by one:', 'blue');
  log('', 'blue');
  log('   Variable Name: DB_HOST', 'blue');
  log('   Value: [paste MYSQL_HOST value]', 'blue');
  log('', 'blue');
  log('   Variable Name: DB_USER', 'blue');
  log('   Value: [paste MYSQL_USER value]', 'blue');
  log('', 'blue');
  log('   Variable Name: DB_PASSWORD', 'blue');
  log('   Value: [paste MYSQL_PASSWORD value]', 'blue');
  log('', 'blue');
  log('   Variable Name: DB_NAME', 'blue');
  log('   Value: [paste MYSQL_DATABASE value]', 'blue');
  log('', 'blue');
  log('   Variable Name: DB_PORT', 'blue');
  log('   Value: 3306', 'blue');
  log('', 'blue');
  log('   Variable Name: DB_SSL', 'blue');
  log('   Value: true', 'blue');

  log('\n📋 STEP 2: Get Backend URL (2 minutes)', 'cyan');
  log('=====================================', 'cyan');
  
  log('\n🔗 2.1 Generate Backend Domain:', 'yellow');
  log('1. Still in "backend-staging" service', 'blue');
  log('2. Click "Settings" tab', 'blue');
  log('3. Scroll to "Networking" section', 'blue');
  log('4. Click "Generate Domain" button', 'blue');
  log('5. Copy the generated URL', 'blue');
  log('   📝 Backend URL: ________________', 'blue');
  log('   (Format: https://backend-staging-production-xxxx.up.railway.app)', 'blue');

  log('\n📝 2.2 Update Frontend Environment:', 'yellow');
  log('After getting the backend URL, update .env.staging file:', 'blue');
  log('VITE_API_URL=https://[your-backend-url]/api', 'blue');

  log('\n📋 STEP 3: Setup Netlify (5 minutes)', 'cyan');
  log('====================================', 'cyan');
  
  log('\n🚀 3.1 Push Staging Branch:', 'yellow');
  log('git push origin staging', 'blue');
  
  log('\n🌐 3.2 Setup Netlify Branch Deploy:', 'yellow');
  log('1. Go to https://app.netlify.com/', 'blue');
  log('2. Select your site (gudang.netlify.app)', 'blue');
  log('3. Site Settings → Build & Deploy', 'blue');
  log('4. Scroll to "Deploy contexts"', 'blue');
  log('5. Enable "Branch deploys"', 'blue');
  log('6. Add "staging" to branch list', 'blue');
  log('7. Save settings', 'blue');
  log('8. Netlify will auto-deploy staging branch', 'blue');
  log('9. Copy the staging URL when ready', 'blue');
  log('   📝 Frontend Staging URL: ________________', 'blue');
  log('   (Format: https://[site-id]--staging.netlify.app)', 'blue');

  log('\n📋 STEP 4: Update CORS (2 minutes)', 'cyan');
  log('=================================', 'cyan');
  
  log('\n🔧 4.1 Update CORS in Railway:', 'yellow');
  log('1. Go back to Railway dashboard', 'blue');
  log('2. Click "backend-staging" service', 'blue');
  log('3. Click "Variables" tab', 'blue');
  log('4. Find "CORS_ORIGIN" variable', 'blue');
  log('5. Update its value to your Netlify staging URL', 'blue');
  log('   Value: https://[your-netlify-staging-url]', 'blue');

  log('\n📋 STEP 5: Test Everything (1 minute)', 'cyan');
  log('====================================', 'cyan');
  
  log('\n🧪 5.1 Test Backend:', 'yellow');
  log('Open these URLs in browser:', 'blue');
  log('https://[your-backend-url]/health', 'blue');
  log('https://[your-backend-url]/api/test-connection', 'blue');
  log('Both should return JSON responses', 'blue');
  
  log('\n🧪 5.2 Test Frontend:', 'yellow');
  log('1. Open https://[your-netlify-staging-url]', 'blue');
  log('2. Try to login/register', 'blue');
  log('3. Check browser console for errors', 'blue');
  log('4. Verify API calls work', 'blue');

  log('\n✅ SUCCESS CRITERIA:', 'green');
  log('====================================', 'green');
  log('✅ Backend health endpoint returns {"success": true}', 'blue');
  log('✅ Database connection test returns success', 'blue');
  log('✅ Frontend staging loads without errors', 'blue');
  log('✅ No CORS errors in browser console', 'blue');
  log('✅ Login/API functionality works', 'blue');

  log('\n🎉 FINAL RESULT:', 'cyan');
  log('====================================', 'cyan');
  log('Production (unchanged):', 'blue');
  log('  Frontend: https://gudang.netlify.app', 'blue');
  log('  Backend: https://gudangmitra-production.up.railway.app', 'blue');
  log('', 'blue');
  log('Staging (new):', 'blue');
  log('  Frontend: https://[your-staging-url]', 'blue');
  log('  Backend: https://[your-backend-url]', 'blue');

  log('\n🔄 WORKFLOW AFTER SETUP:', 'cyan');
  log('====================================', 'cyan');
  log('Development → Staging:', 'blue');
  log('  git checkout staging', 'blue');
  log('  # make changes', 'blue');
  log('  git push origin staging  # auto-deploy', 'blue');
  log('', 'blue');
  log('Staging → Production:', 'blue');
  log('  git checkout main', 'blue');
  log('  git merge staging', 'blue');
  log('  git push origin main     # auto-deploy', 'blue');

  log('\n🆘 NEED HELP?', 'yellow');
  log('If you get stuck on any step:', 'blue');
  log('1. Check Railway service logs for errors', 'blue');
  log('2. Verify all environment variables are set correctly', 'blue');
  log('3. Make sure URLs don\'t have typos', 'blue');
  log('4. Check browser console for frontend errors', 'blue');

  log('\n✅ Ready to start! Begin with STEP 1.', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { main };
