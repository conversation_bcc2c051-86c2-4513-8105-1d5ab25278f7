#!/usr/bin/env node

// Explicit starter script for Railway deployment
console.log('🚀 Starting Gudang Mitra Backend...');
console.log('Environment:', process.env.NODE_ENV || 'production');
console.log('Using railway-server.js as main server file');

// Set NODE_ENV if not set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'staging';
  console.log('Set NODE_ENV to staging');
}

// Start the railway server
require('./railway-server.js');
