redirectsOrigin = "config"
plugins = []
headers = []

[functions]

[functions."*"]

[build]
publish = "C:\\Users\\<USER>\\Downloads\\gudangsub-main\\gudangsub-main\\dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build:staging"

[build.environment]
VITE_API_URL = "https://gudangmitra-staging.up.railway.app/api"
VITE_APP_ENV = "staging"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]