#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description, options = {}) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

// Check if Railway CLI is installed
function checkRailwayCLI() {
  try {
    execSync('railway --version', { stdio: 'pipe' });
    log('✅ Railway CLI is installed', 'green');
    return true;
  } catch (error) {
    log('❌ Railway CLI not found', 'red');
    log('📥 Please install Railway CLI first:', 'yellow');
    log('   npm install -g @railway/cli', 'blue');
    log('   or visit: https://docs.railway.app/develop/cli', 'blue');
    return false;
  }
}

// Check if user is logged in to Railway
function checkRailwayAuth() {
  try {
    const result = execSync('railway whoami', { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ Logged in to Railway as: ${result.trim()}`, 'green');
    return true;
  } catch (error) {
    log('❌ Not logged in to Railway', 'red');
    log('🔐 Please login first: railway login', 'yellow');
    return false;
  }
}

// Create new Railway project for staging
function createStagingProject() {
  log('\n📦 Creating new Railway project for staging...', 'cyan');

  try {
    // Create new project using init command
    log('Creating project "gudangmitra-staging"...', 'blue');
    executeCommand('railway init', 'Creating Railway project');

    log('✅ Staging project created and linked', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to create staging project', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Add MySQL database to the project
function addMySQLDatabase() {
  log('\n🗄️ Adding MySQL database...', 'cyan');

  try {
    executeCommand('railway add', 'Adding MySQL database');
    log('✅ MySQL database added to staging project', 'green');

    // Wait for database to be ready
    log('⏳ Waiting for database to initialize...', 'yellow');
    setTimeout(() => {}, 5000); // Wait 5 seconds

    return true;
  } catch (error) {
    log('❌ Failed to add MySQL database', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Get database connection details
function getDatabaseDetails() {
  log('\n📋 Getting database connection details...', 'cyan');

  try {
    // Get all variables and parse them
    const variablesOutput = execSync('railway variables', { encoding: 'utf8', stdio: 'pipe' });

    // Parse variables from output
    const lines = variablesOutput.split('\n');
    const variables = {};

    for (const line of lines) {
      const match = line.match(/^([A-Z_]+)=(.+)$/);
      if (match) {
        variables[match[1]] = match[2];
      }
    }

    const dbDetails = {
      host: variables.MYSQLHOST || variables.MYSQL_HOST,
      user: variables.MYSQLUSER || variables.MYSQL_USER,
      password: variables.MYSQLPASSWORD || variables.MYSQL_PASSWORD,
      database: variables.MYSQLDATABASE || variables.MYSQL_DATABASE,
      port: variables.MYSQLPORT || variables.MYSQL_PORT || '3306'
    };

    if (!dbDetails.host || !dbDetails.user || !dbDetails.password || !dbDetails.database) {
      throw new Error('Missing required database variables');
    }

    log('✅ Database details retrieved successfully', 'green');
    log(`   Host: ${dbDetails.host}`, 'blue');
    log(`   Database: ${dbDetails.database}`, 'blue');
    log(`   User: ${dbDetails.user}`, 'blue');
    log(`   Port: ${dbDetails.port}`, 'blue');

    return dbDetails;
  } catch (error) {
    log('❌ Failed to get database details', 'red');
    log('This might happen if the database is still initializing', 'yellow');
    log('Error: ' + error.message, 'red');
    return null;
  }
}

// Update .env.staging file with database details
function updateEnvFile(dbDetails) {
  log('\n📝 Updating .env.staging file...', 'cyan');
  
  const envPath = path.join(__dirname, 'server', '.env.staging');
  
  try {
    const envContent = `# Staging Environment Variables
# Railway Database Credentials (Auto-generated on ${new Date().toISOString()})

DB_HOST=${dbDetails.host}
DB_USER=${dbDetails.user}
DB_PASSWORD=${dbDetails.password}
DB_NAME=${dbDetails.database}
DB_PORT=${dbDetails.port}
DB_SSL=true

PORT=3002
NODE_ENV=staging

# CORS Configuration - Will be updated after Netlify staging setup
CORS_ORIGIN=https://gudang-staging.netlify.app

# Security
JWT_SECRET=staging-jwt-secret-${Math.random().toString(36).substring(2, 15)}
`;

    fs.writeFileSync(envPath, envContent);
    log('✅ .env.staging file updated with database credentials', 'green');
    
    // Also create a backup
    const backupPath = path.join(__dirname, 'server', '.env.staging.backup');
    fs.writeFileSync(backupPath, envContent);
    log('✅ Backup created at .env.staging.backup', 'green');
    
    return true;
  } catch (error) {
    log('❌ Failed to update .env.staging file', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Deploy backend to Railway staging
function deployBackend() {
  log('\n🚀 Deploying backend to Railway staging...', 'cyan');
  
  try {
    // Copy railway-staging.toml to railway.toml temporarily
    const stagingConfig = fs.readFileSync('railway-staging.toml', 'utf8');
    const originalConfig = fs.existsSync('railway.toml') ? fs.readFileSync('railway.toml', 'utf8') : null;
    
    fs.writeFileSync('railway.toml', stagingConfig);
    log('✅ Switched to staging configuration', 'green');
    
    // Deploy
    executeCommand('railway up', 'Deploying to Railway');
    
    // Restore original railway.toml if it existed
    if (originalConfig) {
      fs.writeFileSync('railway.toml', originalConfig);
      log('✅ Restored original railway.toml', 'green');
    }
    
    log('✅ Backend deployed to Railway staging', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to deploy backend', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Get the deployed backend URL
function getBackendURL() {
  log('\n🌐 Getting backend URL...', 'cyan');
  
  try {
    const url = execSync('railway domain', { encoding: 'utf8', stdio: 'pipe' }).trim();
    log(`✅ Backend URL: ${url}`, 'green');
    
    // Update frontend .env.staging
    const frontendEnvPath = path.join(__dirname, '.env.staging');
    const frontendEnvContent = `# Staging Environment Variables
VITE_API_URL=${url}/api
VITE_APP_ENV=staging
`;
    
    fs.writeFileSync(frontendEnvPath, frontendEnvContent);
    log('✅ Frontend .env.staging updated with backend URL', 'green');
    
    return url;
  } catch (error) {
    log('❌ Failed to get backend URL', 'red');
    log('Error: ' + error.message, 'red');
    return null;
  }
}

// Main setup function
async function main() {
  log('🎯 Railway Staging Database Setup', 'cyan');
  log('==================================', 'cyan');
  
  // Check prerequisites
  if (!checkRailwayCLI()) {
    process.exit(1);
  }
  
  if (!checkRailwayAuth()) {
    process.exit(1);
  }
  
  // Setup process
  log('\n🔄 Starting setup process...', 'cyan');
  
  // Step 1: Create staging project
  if (!createStagingProject()) {
    process.exit(1);
  }
  
  // Step 2: Add MySQL database
  if (!addMySQLDatabase()) {
    process.exit(1);
  }
  
  // Wait a moment for database to be ready
  log('\n⏳ Waiting for database to be ready...', 'yellow');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // Step 3: Get database details
  const dbDetails = getDatabaseDetails();
  if (!dbDetails) {
    log('\n⚠️  Database details not available yet', 'yellow');
    log('Please wait a few minutes and run this script again', 'yellow');
    log('Or manually get the credentials from Railway dashboard', 'yellow');
    process.exit(1);
  }
  
  // Step 4: Update environment file
  if (!updateEnvFile(dbDetails)) {
    process.exit(1);
  }
  
  // Step 5: Deploy backend
  if (!deployBackend()) {
    process.exit(1);
  }
  
  // Step 6: Get backend URL
  const backendURL = getBackendURL();
  if (!backendURL) {
    process.exit(1);
  }
  
  // Success summary
  log('\n🎉 Railway Staging Setup Complete!', 'green');
  log('=====================================', 'green');
  log('✅ Staging project created', 'green');
  log('✅ MySQL database added', 'green');
  log('✅ Environment files updated', 'green');
  log('✅ Backend deployed', 'green');
  log('\n📋 Next Steps:', 'cyan');
  log('1. Setup Netlify staging branch', 'yellow');
  log('2. Deploy frontend to Netlify', 'yellow');
  log('3. Test the staging environment', 'yellow');
  log(`\n🔗 Backend URL: ${backendURL}`, 'blue');
  log('📁 Database credentials saved to: server/.env.staging', 'blue');
}

// Run the setup
if (require.main === module) {
  main().catch(error => {
    log('❌ Setup failed: ' + error.message, 'red');
    process.exit(1);
  });
}

module.exports = { main };
