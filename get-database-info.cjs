#!/usr/bin/env node

const { execSync } = require('child_process');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

async function main() {
  log('🔍 Getting Railway Database Information', 'cyan');
  log('======================================', 'cyan');

  // Check current status
  const status = executeCommand('railway status', 'Checking Railway status');
  console.log(status);

  log('\n📋 Manual Steps to Get Database Credentials:', 'yellow');
  log('1. Open Railway dashboard: https://railway.com/project/************************************', 'blue');
  log('2. You should see two services:', 'blue');
  log('   - backend-staging (your Node.js app)', 'blue');
  log('   - MySQL (your database)', 'blue');
  log('3. Click on the MySQL service', 'blue');
  log('4. Go to "Variables" tab', 'blue');
  log('5. Copy these values:', 'blue');
  log('   - MYSQL_HOST', 'blue');
  log('   - MYSQL_USER', 'blue');
  log('   - MYSQL_PASSWORD', 'blue');
  log('   - MYSQL_DATABASE', 'blue');
  log('   - MYSQL_PORT (usually 3306)', 'blue');

  log('\n📝 Then update server/.env.staging with these values:', 'yellow');
  log('DB_HOST=[MYSQL_HOST value]', 'blue');
  log('DB_USER=[MYSQL_USER value]', 'blue');
  log('DB_PASSWORD=[MYSQL_PASSWORD value]', 'blue');
  log('DB_NAME=[MYSQL_DATABASE value]', 'blue');
  log('DB_PORT=3306', 'blue');
  log('DB_SSL=true', 'blue');

  log('\n🔧 After updating, set these variables in backend-staging service:', 'yellow');
  log('1. Go back to Railway dashboard', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Go to "Variables" tab', 'blue');
  log('4. Add/update these variables:', 'blue');
  log('   DB_HOST=[same as MYSQL_HOST]', 'blue');
  log('   DB_USER=[same as MYSQL_USER]', 'blue');
  log('   DB_PASSWORD=[same as MYSQL_PASSWORD]', 'blue');
  log('   DB_NAME=[same as MYSQL_DATABASE]', 'blue');
  log('   DB_PORT=3306', 'blue');
  log('   DB_SSL=true', 'blue');

  log('\n⚡ Quick Railway Commands:', 'cyan');
  log('# Set variables via CLI (after you get the values):', 'blue');
  log('railway variables --set "DB_HOST=[mysql-host]"', 'blue');
  log('railway variables --set "DB_USER=[mysql-user]"', 'blue');
  log('railway variables --set "DB_PASSWORD=[mysql-password]"', 'blue');
  log('railway variables --set "DB_NAME=[mysql-database]"', 'blue');
  log('railway variables --set "DB_PORT=3306"', 'blue');
  log('railway variables --set "DB_SSL=true"', 'blue');

  log('\n✅ After completing this step, run: node get-backend-url.cjs', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { main };
