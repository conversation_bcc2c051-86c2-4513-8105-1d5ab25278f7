#!/usr/bin/env node

const { execSync } = require('child_process');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description, silent = false) {
  try {
    if (!silent) log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8' });
    if (!silent) log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    if (!silent) log(`❌ Error during ${description}:`, 'red');
    if (!silent) log(error.message, 'red');
    return null;
  }
}

async function main() {
  log('🔧 Auto-Setup Database Connection', 'cyan');
  log('=================================', 'cyan');

  // Try to get services list
  log('\n📋 Attempting to get database variables automatically...', 'blue');
  
  // First, let's see what services are available
  const status = executeCommand('railway status', 'Checking current status');
  console.log(status);

  // Try to list services (this might not work in non-interactive mode)
  log('\n🔍 Trying to detect MySQL service...', 'yellow');
  
  // Since we can't easily switch services via CLI in non-interactive mode,
  // let's provide a guided approach
  log('\n📋 Guided Database Setup:', 'cyan');
  log('Since Railway CLI service switching requires interactive mode,', 'blue');
  log('please follow these steps manually:', 'blue');

  log('\n🌐 Step 1: Open Railway Dashboard', 'yellow');
  log('URL: https://railway.com/project/************************************', 'blue');

  log('\n🗄️  Step 2: Get MySQL Variables', 'yellow');
  log('1. Click on "MySQL" service (database icon)', 'blue');
  log('2. Click "Variables" tab', 'blue');
  log('3. You should see variables like:', 'blue');
  log('   - MYSQL_HOST (e.g., mysql.railway.internal)', 'blue');
  log('   - MYSQL_USER (e.g., root)', 'blue');
  log('   - MYSQL_PASSWORD (long random string)', 'blue');
  log('   - MYSQL_DATABASE (e.g., railway)', 'blue');
  log('   - MYSQL_PORT (usually 3306)', 'blue');
  log('   - MYSQL_URL (full connection string)', 'blue');

  log('\n⚡ Step 3: Set Variables in Backend Service', 'yellow');
  log('After getting the MySQL values, run these commands:', 'blue');
  log('(Replace [values] with actual values from MySQL service)', 'blue');
  log('', 'blue');
  log('railway variables --set "DB_HOST=[MYSQL_HOST]"', 'blue');
  log('railway variables --set "DB_USER=[MYSQL_USER]"', 'blue');
  log('railway variables --set "DB_PASSWORD=[MYSQL_PASSWORD]"', 'blue');
  log('railway variables --set "DB_NAME=[MYSQL_DATABASE]"', 'blue');
  log('railway variables --set "DB_PORT=3306"', 'blue');
  log('railway variables --set "DB_SSL=true"', 'blue');

  log('\n📝 Step 4: Verify Variables', 'yellow');
  log('After setting variables, check them:', 'blue');
  log('railway variables', 'blue');

  log('\n🔄 Step 5: Redeploy Backend', 'yellow');
  log('To apply new database variables:', 'blue');
  log('railway up', 'blue');

  log('\n💡 Example Commands (replace with your actual values):', 'cyan');
  log('# Example MySQL values (yours will be different):', 'blue');
  log('railway variables --set "DB_HOST=mysql.railway.internal"', 'blue');
  log('railway variables --set "DB_USER=root"', 'blue');
  log('railway variables --set "DB_PASSWORD=abc123xyz789"', 'blue');
  log('railway variables --set "DB_NAME=railway"', 'blue');
  log('railway variables --set "DB_PORT=3306"', 'blue');
  log('railway variables --set "DB_SSL=true"', 'blue');

  log('\n✅ After completing database setup:', 'green');
  log('Run: node get-backend-url.cjs', 'yellow');

  log('\n🆘 Need Help?', 'cyan');
  log('If you get stuck, you can:', 'blue');
  log('1. Check Railway dashboard for MySQL service variables', 'blue');
  log('2. Copy-paste the exact values (including quotes if needed)', 'blue');
  log('3. Make sure you\'re in the backend-staging service when setting variables', 'blue');
}

if (require.main === module) {
  main();
}

module.exports = { main };
