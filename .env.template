# Frontend Environment Variables Template
# Copy this file to .env.production and .env.staging, then fill with actual values

# API URL - Backend endpoint
VITE_API_URL=https://your-backend-url.up.railway.app/api

# App Environment
VITE_APP_ENV=production

# Instructions:
# 1. Copy this file to .env.production and .env.staging
# 2. Replace placeholder URLs with actual backend URLs
# 3. For staging: use staging backend URL and set VITE_APP_ENV=staging
# 4. Never commit actual .env files to git
