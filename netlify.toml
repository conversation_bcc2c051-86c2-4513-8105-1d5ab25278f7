[build]
  command = "npm run build:staging"
  publish = "dist"

# Production environment (main branch)
[context.production.environment]
  VITE_API_URL = "https://gudangmitra-production.up.railway.app/api"
  VITE_APP_ENV = "production"

# Staging environment (staging branch and branch deploys)
[context.staging.environment]
  VITE_API_URL = "https://backend-staging-production.up.railway.app/api"
  VITE_APP_ENV = "staging"

[context.branch-deploy.environment]
  VITE_API_URL = "https://backend-staging-production.up.railway.app/api"
  VITE_APP_ENV = "staging"

[context.deploy-preview.environment]
  VITE_API_URL = "https://backend-staging-production.up.railway.app/api"
  VITE_APP_ENV = "staging"

# Redirect all routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Force deploy trigger for staging
# Updated: 2025-07-15
