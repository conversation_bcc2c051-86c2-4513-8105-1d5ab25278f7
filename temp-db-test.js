
        require('dotenv').config({ path: '.env.staging' });
        const mysql = require('mysql2/promise');
        
        async function testConnection() {
          try {
            const connection = await mysql.createConnection({
              host: process.env.DB_HOST,
              user: process.env.DB_USER,
              password: process.env.DB_PASSWORD,
              database: process.env.DB_NAME,
              port: process.env.DB_PORT,
              ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
            });
            
            await connection.execute('SELECT 1');
            await connection.end();
            console.log('SUCCESS');
          } catch (error) {
            console.log('ERROR:', error.message);
          }
        }
        
        testConnection();
      