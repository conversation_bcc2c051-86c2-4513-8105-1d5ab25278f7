<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache - Gudang Mitra</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background-color: #2563eb;
        }
        .success {
            color: #059669;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Clear Cache & Refresh</h1>
        <p>Klik tombol di bawah untuk membersihkan cache dan memuat ulang aplikasi dengan data terbaru.</p>
        
        <button class="btn" onclick="clearCacheAndReload()">Clear Cache & Reload</button>
        <button class="btn" onclick="goToApp()">Go to App</button>
        
        <div id="status"></div>
        
        <hr style="margin: 30px 0;">
        
        <h3>Manual Steps:</h3>
        <ol style="text-align: left;">
            <li>Press <strong>Ctrl + Shift + R</strong> (Windows) or <strong>Cmd + Shift + R</strong> (Mac) for hard refresh</li>
            <li>Or press <strong>F12</strong> → go to <strong>Application</strong> tab → <strong>Storage</strong> → <strong>Clear site data</strong></li>
            <li>Then reload the page</li>
        </ol>
    </div>

    <script>
        function clearCacheAndReload() {
            const status = document.getElementById('status');
            
            try {
                // Clear localStorage
                localStorage.clear();
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear cookies for this domain
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                status.innerHTML = '<div class="success">✅ Cache cleared! Redirecting...</div>';
                
                // Redirect to main app after 2 seconds
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
                
            } catch (error) {
                status.innerHTML = '<div style="color: red;">❌ Error: ' + error.message + '</div>';
            }
        }
        
        function goToApp() {
            window.location.href = '/';
        }
        
        // Auto-clear cache when page loads
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auto') === 'true') {
                clearCacheAndReload();
            }
        };
    </script>
</body>
</html>
