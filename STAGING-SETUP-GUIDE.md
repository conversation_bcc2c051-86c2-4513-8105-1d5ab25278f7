# 🚀 Staging Environment Setup Guide

Panduan lengkap untuk setup dan menggunakan environment staging sebagai cadangan dari production.

## 📋 Overview

Sekarang aplikasi memiliki 2 environment:
- **Production**: Environment utama untuk users
- **Staging**: Environment cadangan untuk testing fitur baru

## 🏗️ Architecture

```
Production Environment:
├── Frontend: https://gudang.netlify.app (branch: main)
├── Backend: https://gudangmitra-production.up.railway.app
└── Database: Production MySQL di Railway

Staging Environment:
├── Frontend: https://[site-id]--branch-staging.netlify.app (branch: staging)
├── Backend: https://gudangmitra-staging.up.railway.app
└── Database: Staging MySQL di Railway (terpisah)
```

## ⚙️ Setup Instructions

### 1. Database Setup (Railway)

#### Production Database:
1. Login ke Railway dashboard
2. Pastikan database production sudah ada dan berjalan
3. Copy connection details ke `server/.env.production`

#### Staging Database:
1. Buat database MySQL baru di Railway untuk staging
2. Copy connection details ke `server/.env.staging`
3. Import schema dari production ke staging (opsional)

### 2. Backend Setup (Railway)

#### Production Backend:
1. Service sudah ada: `gudangmitra-production`
2. Set environment variables di Railway:
   ```
   NODE_ENV=production
   DB_HOST=[production-db-host]
   DB_USER=[production-db-user]
   DB_PASSWORD=[production-db-password]
   DB_NAME=gudang1
   CORS_ORIGIN=https://gudang.netlify.app
   ```

#### Staging Backend:
1. Buat service baru di Railway: `gudangmitra-staging`
2. Deploy dari repository yang sama
3. Set environment variables:
   ```
   NODE_ENV=staging
   DB_HOST=[staging-db-host]
   DB_USER=[staging-db-user]
   DB_PASSWORD=[staging-db-password]
   DB_NAME=gudang_staging
   CORS_ORIGIN=https://[site-id]--branch-staging.netlify.app
   ```
4. Deploy menggunakan `railway-staging.toml`

### 3. Frontend Setup (Netlify)

#### Production Frontend:
1. Site sudah ada dan terhubung ke branch `main`
2. Environment variables sudah diset via `netlify.toml`

#### Staging Frontend:
1. Di Netlify dashboard, enable "Branch deploys"
2. Buat branch `staging` dari `main`
3. Push ke branch `staging` → Netlify akan auto-deploy ke staging URL
4. Update `CORS_ORIGIN` di staging backend dengan URL staging yang didapat

## 🚀 Deployment Workflow

### Development ke Staging:
```bash
# 1. Buat/switch ke branch staging
git checkout -b staging
# atau
git checkout staging

# 2. Buat perubahan kode
# ... edit files ...

# 3. Test lokal dengan staging config
npm run dev:staging

# 4. Build untuk staging
npm run build:staging

# 5. Deploy preparation
node deploy-staging.js

# 6. Push ke staging branch
git add .
git commit -m "feat: new feature for testing"
git push origin staging

# 7. Deploy backend ke Railway staging
railway up --environment staging
```

### Staging ke Production:
```bash
# 1. Test staging environment
node test-environments.js

# 2. Jika semua OK, merge ke main
git checkout main
git merge staging

# 3. Push ke production
git push origin main

# 4. Deploy backend ke Railway production (jika perlu)
railway up --environment production
```

## 🧪 Testing

### Test Environment Health:
```bash
# Test semua endpoints
npm run test:env
node test-environments.js

# Test backend lokal
npm run server:staging
# atau
npm run server:production
```

### Test Frontend Lokal:
```bash
# Development mode dengan staging API
npm run dev:staging

# Development mode dengan production API
npm run dev

# Preview staging build
npm run preview:staging
```

## 📁 File Structure

```
project/
├── .env.production          # Frontend production env
├── .env.staging            # Frontend staging env
├── netlify.toml            # Netlify config (branch-based)
├── railway-staging.toml    # Railway staging config
├── deploy-staging.js       # Staging deployment script
├── test-environments.js    # Environment testing script
└── server/
    ├── .env.production     # Backend production env
    ├── .env.staging       # Backend staging env
    └── railway-server.js  # Dynamic env loading
```

## 🔧 Environment Variables

### Frontend (.env.production / .env.staging):
```env
VITE_API_URL=https://gudangmitra-[env].up.railway.app/api
VITE_APP_ENV=production|staging
```

### Backend (server/.env.production / server/.env.staging):
```env
NODE_ENV=production|staging
DB_HOST=[database-host]
DB_USER=[database-user]
DB_PASSWORD=[database-password]
DB_NAME=gudang1|gudang_staging
CORS_ORIGIN=[frontend-url]
```

## 🛠️ Available Scripts

### Frontend:
- `npm run dev` - Development dengan production API
- `npm run dev:staging` - Development dengan staging API
- `npm run build:production` - Build untuk production
- `npm run build:staging` - Build untuk staging
- `npm run deploy:staging` - Prepare staging deployment

### Backend:
- `npm run start:production` - Start production server
- `npm run start:staging` - Start staging server
- `npm run dev:staging` - Development staging server

## 🔍 Troubleshooting

### CORS Issues:
1. Pastikan `CORS_ORIGIN` di backend sesuai dengan URL frontend
2. Check logs di Railway untuk CORS errors
3. Verify environment variables di Railway dashboard

### Database Connection:
1. Test connection: `npm run test:connection`
2. Check database status di Railway dashboard
3. Verify environment variables

### Build Issues:
1. Clear node_modules: `rm -rf node_modules && npm ci`
2. Check environment variables
3. Run linting: `npm run lint`

### Deployment Issues:
1. Check Railway logs
2. Verify branch is pushed to Git
3. Check Netlify build logs

## 📚 Best Practices

1. **Always test in staging first** sebelum deploy ke production
2. **Keep staging data separate** dari production
3. **Use meaningful commit messages** untuk tracking changes
4. **Monitor both environments** untuk performance issues
5. **Backup production database** sebelum major changes

## 🔗 Useful Links

- [Railway Dashboard](https://railway.app/dashboard)
- [Netlify Dashboard](https://app.netlify.com/)
- [Production Frontend](https://gudang.netlify.app)
- [Production Backend](https://gudangmitra-production.up.railway.app)
- [Staging Backend](https://gudangmitra-staging.up.railway.app)

## 🆘 Support

Jika ada masalah:
1. Check logs di Railway/Netlify dashboard
2. Run `node test-environments.js` untuk health check
3. Verify environment variables
4. Check CORS configuration
