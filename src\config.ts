// Environment detection
export const APP_ENV = import.meta.env.VITE_APP_ENV || 'production';
export const IS_PRODUCTION = APP_ENV === 'production';
export const IS_STAGING = APP_ENV === 'staging';
export const IS_DEVELOPMENT = import.meta.env.DEV;

// API configuration with environment-specific fallbacks
export const API_BASE_URL = import.meta.env.VITE_API_URL || 
  (IS_STAGING ? 'https://gudangmitra-staging.up.railway.app/api' : 'https://gudangmitra-production.up.railway.app/api');

// Application configuration
export const APP_NAME = IS_STAGING ? 'Gudang Mitra (Staging)' : 'Gudang Mitra';
export const APP_VERSION = '1.0.0';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;

// Debug logging for non-production environments
export const DEBUG_MODE = !IS_PRODUCTION;

// Environment info for debugging
if (DEBUG_MODE) {
  console.log('🔧 Environment Configuration:', {
    APP_ENV,
    API_BASE_URL,
    IS_PRODUCTION,
    IS_STAGING,
    IS_DEVELOPMENT
  });
}
