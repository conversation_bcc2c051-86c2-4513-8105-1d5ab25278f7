#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description, options = {}) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

// Check if git is initialized
function checkGitRepo() {
  try {
    execSync('git status', { stdio: 'pipe' });
    log('✅ Git repository found', 'green');
    return true;
  } catch (error) {
    log('❌ Not a git repository', 'red');
    log('Please initialize git first: git init', 'yellow');
    return false;
  }
}

// Check if Netlify CLI is installed
function checkNetlifyCLI() {
  try {
    execSync('netlify --version', { stdio: 'pipe' });
    log('✅ Netlify CLI is installed', 'green');
    return true;
  } catch (error) {
    log('❌ Netlify CLI not found', 'red');
    log('📥 Please install Netlify CLI first:', 'yellow');
    log('   npm install -g netlify-cli', 'blue');
    log('   or visit: https://docs.netlify.com/cli/get-started/', 'blue');
    return false;
  }
}

// Check if user is logged in to Netlify
function checkNetlifyAuth() {
  try {
    const result = execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
    if (result.includes('Not logged in')) {
      log('❌ Not logged in to Netlify', 'red');
      log('🔐 Please login first: netlify login', 'yellow');
      return false;
    }
    log('✅ Logged in to Netlify', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to check Netlify status', 'red');
    log('🔐 Please login first: netlify login', 'yellow');
    return false;
  }
}

// Create staging branch
function createStagingBranch() {
  log('\n🌿 Creating staging branch...', 'cyan');
  
  try {
    // Check if staging branch already exists
    try {
      execSync('git show-ref --verify --quiet refs/heads/staging', { stdio: 'pipe' });
      log('⚠️  Staging branch already exists', 'yellow');
      
      // Switch to staging branch
      executeCommand('git checkout staging', 'Switching to staging branch');
      return true;
    } catch (error) {
      // Branch doesn't exist, create it
      executeCommand('git checkout -b staging', 'Creating staging branch');
      return true;
    }
  } catch (error) {
    log('❌ Failed to create staging branch', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Update netlify.toml for staging
function updateNetlifyConfig() {
  log('\n📝 Updating netlify.toml for staging...', 'cyan');
  
  try {
    const netlifyConfig = `[build]
  command = "npm run build:staging"
  publish = "dist"

# Production environment (main branch)
[context.production.environment]
  VITE_API_URL = "https://gudangmitra-production.up.railway.app/api"
  VITE_APP_ENV = "production"

# Staging environment (staging branch and branch deploys)
[context.staging.environment]
  VITE_API_URL = "https://gudangmitra-staging.up.railway.app/api"
  VITE_APP_ENV = "staging"

[context.branch-deploy.environment]
  VITE_API_URL = "https://gudangmitra-staging.up.railway.app/api"
  VITE_APP_ENV = "staging"

[context.deploy-preview.environment]
  VITE_API_URL = "https://gudangmitra-staging.up.railway.app/api"
  VITE_APP_ENV = "staging"

# Redirect all routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
`;

    fs.writeFileSync('netlify.toml', netlifyConfig);
    log('✅ netlify.toml updated for staging deployment', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to update netlify.toml', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Build staging version
function buildStaging() {
  log('\n🔨 Building staging version...', 'cyan');
  
  try {
    executeCommand('npm run build:staging', 'Building staging application');
    log('✅ Staging build completed', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to build staging version', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Commit and push staging branch
function commitAndPush() {
  log('\n📤 Committing and pushing staging branch...', 'cyan');
  
  try {
    // Add all changes
    executeCommand('git add .', 'Adding changes to git');
    
    // Commit changes
    const commitMessage = `feat: setup staging environment
    
- Add staging branch for deployment
- Update netlify.toml for staging context
- Configure staging environment variables
- Ready for Netlify staging deployment`;
    
    executeCommand(`git commit -m "${commitMessage}"`, 'Committing changes');
    
    // Push staging branch
    executeCommand('git push -u origin staging', 'Pushing staging branch to remote');
    
    log('✅ Staging branch pushed to remote repository', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to commit and push', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Deploy to Netlify
function deployToNetlify() {
  log('\n🚀 Deploying to Netlify...', 'cyan');
  
  try {
    // Check if site is already linked
    try {
      const siteInfo = execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
      if (siteInfo.includes('Site name:')) {
        log('✅ Site already linked to Netlify', 'green');
      }
    } catch (error) {
      // Site not linked, create new site
      log('Creating new Netlify site...', 'blue');
      executeCommand('netlify sites:create --name gudang-staging', 'Creating Netlify site');
    }
    
    // Deploy the site
    executeCommand('netlify deploy --prod --dir=dist', 'Deploying to Netlify');
    
    // Get site URL
    const siteInfo = execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
    const urlMatch = siteInfo.match(/URL:\s*(https?:\/\/[^\s]+)/);
    const siteUrl = urlMatch ? urlMatch[1] : 'https://gudang-staging.netlify.app';
    
    log(`✅ Site deployed to: ${siteUrl}`, 'green');
    return siteUrl;
  } catch (error) {
    log('❌ Failed to deploy to Netlify', 'red');
    log('Error: ' + error.message, 'red');
    return null;
  }
}

// Update CORS origin in backend
function updateCORSOrigin(siteUrl) {
  log('\n🔧 Updating CORS origin in backend...', 'cyan');
  
  try {
    const envPath = path.join(__dirname, 'server', '.env.staging');
    
    if (fs.existsSync(envPath)) {
      let envContent = fs.readFileSync(envPath, 'utf8');
      envContent = envContent.replace(
        /CORS_ORIGIN=.*/,
        `CORS_ORIGIN=${siteUrl}`
      );
      
      fs.writeFileSync(envPath, envContent);
      log('✅ CORS origin updated in server/.env.staging', 'green');
      log(`   New CORS origin: ${siteUrl}`, 'blue');
      
      log('\n⚠️  Remember to redeploy backend to Railway with updated CORS settings', 'yellow');
      return true;
    } else {
      log('❌ server/.env.staging not found', 'red');
      return false;
    }
  } catch (error) {
    log('❌ Failed to update CORS origin', 'red');
    log('Error: ' + error.message, 'red');
    return false;
  }
}

// Main setup function
async function main() {
  log('🎯 Netlify Staging Setup', 'cyan');
  log('========================', 'cyan');
  
  // Check prerequisites
  if (!checkGitRepo()) {
    process.exit(1);
  }
  
  if (!checkNetlifyCLI()) {
    process.exit(1);
  }
  
  if (!checkNetlifyAuth()) {
    process.exit(1);
  }
  
  // Setup process
  log('\n🔄 Starting Netlify staging setup...', 'cyan');
  
  // Step 1: Create staging branch
  if (!createStagingBranch()) {
    process.exit(1);
  }
  
  // Step 2: Update Netlify configuration
  if (!updateNetlifyConfig()) {
    process.exit(1);
  }
  
  // Step 3: Build staging version
  if (!buildStaging()) {
    process.exit(1);
  }
  
  // Step 4: Commit and push
  if (!commitAndPush()) {
    process.exit(1);
  }
  
  // Step 5: Deploy to Netlify
  const siteUrl = deployToNetlify();
  if (!siteUrl) {
    process.exit(1);
  }
  
  // Step 6: Update CORS origin
  updateCORSOrigin(siteUrl);
  
  // Success summary
  log('\n🎉 Netlify Staging Setup Complete!', 'green');
  log('===================================', 'green');
  log('✅ Staging branch created', 'green');
  log('✅ Netlify configuration updated', 'green');
  log('✅ Staging build completed', 'green');
  log('✅ Changes committed and pushed', 'green');
  log('✅ Site deployed to Netlify', 'green');
  log('\n📋 Next Steps:', 'cyan');
  log('1. Redeploy backend to Railway with updated CORS', 'yellow');
  log('2. Test the staging environment', 'yellow');
  log('3. Setup branch protection rules (optional)', 'yellow');
  log(`\n🔗 Staging Site URL: ${siteUrl}`, 'blue');
  log('📁 Staging branch: staging', 'blue');
}

// Run the setup
if (require.main === module) {
  main().catch(error => {
    log('❌ Setup failed: ' + error.message, 'red');
    process.exit(1);
  });
}

module.exports = { main };
