#!/usr/bin/env node

const https = require('https');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🔍  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

// Test API endpoint
function testEndpoint(url, description) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const responseTime = Date.now() - startTime;
        
        try {
          const jsonData = JSON.parse(data);
          
          if (res.statusCode === 200) {
            log(`✅ ${description}`, 'green');
            log(`   Status: ${res.statusCode}`, 'green');
            log(`   Response time: ${responseTime}ms`, 'blue');
            
            if (jsonData.totalUsers !== undefined) {
              log(`   Total Users: ${jsonData.totalUsers}`, 'blue');
            }
            if (jsonData.totalItems !== undefined) {
              log(`   Total Items: ${jsonData.totalItems}`, 'blue');
            }
            if (jsonData.totalRequests !== undefined) {
              log(`   Total Requests: ${jsonData.totalRequests}`, 'blue');
            }
            
            resolve({ success: true, data: jsonData, status: res.statusCode });
          } else {
            log(`❌ ${description}`, 'red');
            log(`   Status: ${res.statusCode}`, 'red');
            log(`   Message: ${jsonData.message || 'Unknown error'}`, 'red');
            resolve({ success: false, data: jsonData, status: res.statusCode });
          }
        } catch (error) {
          log(`❌ ${description}`, 'red');
          log(`   Status: ${res.statusCode}`, 'red');
          log(`   Parse Error: ${error.message}`, 'red');
          log(`   Raw Response: ${data.substring(0, 200)}...`, 'yellow');
          resolve({ success: false, error: error.message, status: res.statusCode });
        }
      });
    }).on('error', (error) => {
      log(`❌ ${description}`, 'red');
      log(`   Network Error: ${error.message}`, 'red');
      resolve({ success: false, error: error.message });
    });
  });
}

async function main() {
  heading('Test Staging Dashboard Endpoints');
  
  const baseUrl = 'https://backend-staging-production.up.railway.app';
  
  const endpoints = [
    { url: `${baseUrl}/`, description: 'Health Check' },
    { url: `${baseUrl}/api/health`, description: 'API Health' },
    { url: `${baseUrl}/api/items`, description: 'Items API (should work)' },
    { url: `${baseUrl}/api/dashboard/stats`, description: 'Dashboard Stats' },
    { url: `${baseUrl}/api/dashboard/users`, description: 'Dashboard Users' },
    { url: `${baseUrl}/api/dashboard/items`, description: 'Dashboard Items' }
  ];
  
  log('\n🔍 Testing all endpoints...', 'cyan');
  
  let successCount = 0;
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint.url, endpoint.description);
    results.push({ ...endpoint, result });
    
    if (result.success) {
      successCount++;
    }
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Summary
  log('\n📊 Test Summary:', 'cyan');
  log(`✅ Successful: ${successCount}/${endpoints.length}`, successCount === endpoints.length ? 'green' : 'yellow');
  log(`❌ Failed: ${endpoints.length - successCount}/${endpoints.length}`, 'red');
  
  // Detailed results
  log('\n📋 Detailed Results:', 'cyan');
  results.forEach(({ description, result }) => {
    const status = result.success ? '✅' : '❌';
    const color = result.success ? 'green' : 'red';
    log(`${status} ${description}: ${result.status || 'Network Error'}`, color);
  });
  
  // Recommendations
  if (successCount < endpoints.length) {
    log('\n💡 Recommendations:', 'yellow');
    
    const failedDashboard = results.filter(r => 
      r.description.includes('Dashboard') && !r.result.success
    );
    
    if (failedDashboard.length > 0) {
      log('   - Dashboard endpoints are not working', 'yellow');
      log('   - Check if Railway deployment is using the correct server file', 'yellow');
      log('   - Verify that server/simple-railway-server.js has dashboard endpoints', 'yellow');
      log('   - Consider redeploying or checking Railway logs', 'yellow');
    }
    
    const workingEndpoints = results.filter(r => r.result.success);
    if (workingEndpoints.length > 0) {
      log('   - Basic server is working (some endpoints respond)', 'green');
      log('   - Database connection appears to be working', 'green');
    }
  } else {
    log('\n🎉 All endpoints are working!', 'green');
    log('   Dashboard should now display correct data from production database', 'green');
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('Script error:', error);
    process.exit(1);
  });
}

module.exports = { testEndpoint, main };
