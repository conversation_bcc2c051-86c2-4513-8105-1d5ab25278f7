# ✅ COMPLETE STAGING SETUP CHECKLIST

## 🎯 **OVERVIEW**
Setup staging environment untuk Gudang Mitra app dengan environment terpisah 100% dari production.

---

## 📋 **CHECKLIST - Follow in Order**

### **☑️ STEP 1: Git Repository Setup (5 minutes)**
**Status: REQUIRED FIRST**

**Option A: Create New Repository (Recommended)**
- [ ] Go to https://github.com/new
- [ ] Repository name: `gudangsub-staging`
- [ ] Description: `Gudang Mitra - Staging Environment`
- [ ] Set visibility (Public/Private)
- [ ] **DO NOT** initialize with README
- [ ] Click "Create repository"
- [ ] Copy the repository URL

**Option B: Use Existing Repository**
- [ ] Get your existing repository URL
- [ ] Ensure you have push access

**Connect Local to Remote:**
```bash
# Replace [username] with your GitHub username
git remote add origin https://github.com/[username]/gudangsub-staging.git

# Push main branch first
git checkout main
git push -u origin main

# Push staging branch
git checkout staging
git push -u origin staging
```

**Verify:**
- [ ] `git remote -v` shows your repository
- [ ] Both `main` and `staging` branches visible on GitHub

---

### **☑️ STEP 2: Railway Database Setup (5 minutes)**
**Status: READY TO DO**

**2.1 Get Database Credentials:**
- [ ] Open: https://railway.com/project/************************************
- [ ] Click "MySQL" service
- [ ] Click "Variables" tab
- [ ] Copy these values:
  - [ ] `MYSQL_HOST`: ________________
  - [ ] `MYSQL_USER`: ________________
  - [ ] `MYSQL_PASSWORD`: ________________
  - [ ] `MYSQL_DATABASE`: ________________
  - [ ] `MYSQL_PORT`: 3306

**2.2 Set Backend Variables:**
- [ ] Go back to project overview
- [ ] Click "backend-staging" service
- [ ] Click "Variables" tab
- [ ] Add these variables one by one:
  - [ ] `DB_HOST` = [MYSQL_HOST value]
  - [ ] `DB_USER` = [MYSQL_USER value]
  - [ ] `DB_PASSWORD` = [MYSQL_PASSWORD value]
  - [ ] `DB_NAME` = [MYSQL_DATABASE value]
  - [ ] `DB_PORT` = 3306
  - [ ] `DB_SSL` = true

---

### **☑️ STEP 3: Get Backend URL (2 minutes)**
**Status: READY TO DO**

**3.1 Generate Domain:**
- [ ] In "backend-staging" service
- [ ] Click "Settings" tab
- [ ] Scroll to "Networking" section
- [ ] Click "Generate Domain" button
- [ ] Copy the URL: ________________
  - Format: `https://backend-staging-production-xxxx.up.railway.app`

**3.2 Update Frontend Config:**
- [ ] Edit `.env.staging` file
- [ ] Update: `VITE_API_URL=https://[your-backend-url]/api`

---

### **☑️ STEP 4: Netlify Branch Deploy (5 minutes)**
**Status: REQUIRES STEP 1 COMPLETED**

**4.1 Connect Repository:**
- [ ] Go to https://app.netlify.com/
- [ ] Click "Add new site" → "Import an existing project"
- [ ] Connect to GitHub
- [ ] Select your repository
- [ ] Deploy settings:
  - [ ] Branch: `main`
  - [ ] Build command: `npm run build`
  - [ ] Publish directory: `dist`
- [ ] Click "Deploy site"

**4.2 Enable Branch Deploys:**
- [ ] Go to Site Settings → Build & Deploy
- [ ] Scroll to "Deploy contexts"
- [ ] Enable "Branch deploys"
- [ ] Add `staging` to branch list
- [ ] Save settings

**4.3 Get Staging URL:**
- [ ] Netlify will auto-deploy staging branch
- [ ] Copy staging URL: ________________
  - Format: `https://[site-id]--staging.netlify.app`

---

### **☑️ STEP 5: Update CORS (2 minutes)**
**Status: REQUIRES STEP 3 & 4 COMPLETED**

**5.1 Update Railway CORS:**
- [ ] Go back to Railway dashboard
- [ ] Click "backend-staging" service
- [ ] Click "Variables" tab
- [ ] Find `CORS_ORIGIN` variable
- [ ] Update value: `https://[your-netlify-staging-url]`

---

### **☑️ STEP 6: Test Everything (3 minutes)**
**Status: FINAL VERIFICATION**

**6.1 Test Backend:**
- [ ] Open: `https://[your-backend-url]/health`
  - Should return: `{"success": true, "message": "Railway server is running", ...}`
- [ ] Open: `https://[your-backend-url]/api/test-connection`
  - Should return: `{"success": true, "message": "Database connection successful"}`

**6.2 Test Frontend:**
- [ ] Open: `https://[your-netlify-staging-url]`
- [ ] Page loads without errors
- [ ] Check browser console (F12) - no CORS errors
- [ ] Try login/register functionality
- [ ] Verify API calls work

---

## 🎉 **SUCCESS CRITERIA**

### **✅ All Green Checkmarks:**
- [ ] Git repository connected and both branches pushed
- [ ] Railway database variables set correctly
- [ ] Backend staging URL accessible and healthy
- [ ] Netlify staging deployment working
- [ ] CORS configured correctly
- [ ] Frontend can communicate with backend
- [ ] No errors in browser console

### **🌐 Final URLs:**
**Production (Unchanged):**
- Frontend: `https://gudang.netlify.app`
- Backend: `https://gudangmitra-production.up.railway.app`

**Staging (New):**
- Frontend: `https://[your-staging-url]`
- Backend: `https://[your-backend-url]`

---

## 🔄 **WORKFLOW AFTER SETUP**

### **Development → Staging:**
```bash
git checkout staging
# make changes to code
git add .
git commit -m "feat: new feature"
git push origin staging  # Auto-deploys to staging
```

### **Staging → Production:**
```bash
# After testing in staging
git checkout main
git merge staging
git push origin main     # Auto-deploys to production
```

---

## 🆘 **TROUBLESHOOTING**

### **❌ Git Issues:**
- **"Permission denied"**: Set up SSH keys or use HTTPS with token
- **"Repository not found"**: Check URL and permissions
- **"Branch already exists"**: Use `git push origin staging` (without -u)

### **❌ Railway Issues:**
- **Database connection fails**: Verify all DB_* variables are set correctly
- **Backend not accessible**: Check if domain is generated and service is deployed
- **Environment variables not working**: Redeploy service after setting variables

### **❌ Netlify Issues:**
- **Build fails**: Check if repository is connected and build command is correct
- **Branch deploy not working**: Verify branch deploys are enabled and staging branch exists
- **Environment variables not loading**: Check netlify.toml configuration

### **❌ CORS Issues:**
- **CORS errors in browser**: Verify CORS_ORIGIN matches exact Netlify staging URL
- **API calls fail**: Check if backend URL in .env.staging is correct

---

## 📚 **HELPER SCRIPTS**

Run these scripts for guidance:
- `node manual-setup-guide.cjs` - Complete manual guide
- `node setup-git-repository.cjs` - Git setup help
- `node push-staging-branch.cjs` - Push branch help
- `node test-staging-complete.cjs` - Final testing

---

## ⏱️ **ESTIMATED TIME**
- **Total Setup Time**: ~20 minutes
- **Step 1 (Git)**: 5 minutes
- **Step 2 (Database)**: 5 minutes  
- **Step 3 (Backend URL)**: 2 minutes
- **Step 4 (Netlify)**: 5 minutes
- **Step 5 (CORS)**: 2 minutes
- **Step 6 (Testing)**: 3 minutes

---

## 🎯 **START HERE**
**Begin with Step 1 (Git Repository Setup) and follow the checklist in order.**

**Current Status: Ready to begin Step 1** ✅
