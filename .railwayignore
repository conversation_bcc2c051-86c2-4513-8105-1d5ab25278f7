# Railway ignore file - exclude unnecessary files from deployment

# Development files
node_modules/
.git/
.vscode/
.idea/

# Test files
test-chat.html
test-chat-api.cjs
*.test.js
*.spec.js

# Build artifacts
dist/
build/

# Logs
*.log
logs/

# Environment files (use Railway environment variables instead)
.env.local
.env.development
.env.test

# Documentation
README.md
docs/

# Source maps
*.map

# Cache
.cache/
.parcel-cache/

# OS files
.DS_Store
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Temporary files
tmp/
temp/

# Setup and deployment scripts
setup-*.cjs
deploy-*.cjs
test-*.cjs
*.md
*.bat
*.ps1

# Frontend files (not needed for backend deployment)
src/
public/
index.html
vite.config.ts
tailwind.config.js
postcss.config.js
tsconfig*.json
eslint.config.js

# Large files
*.msi
*.exe
backup.sql
