#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🚀  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

function runCommand(command, description) {
  try {
    log(`🔄 ${description}...`, 'blue');
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} completed`, 'green');
    return output;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'red');
    throw error;
  }
}

async function main() {
  heading('Manual Netlify Deploy for Staging');
  
  try {
    // Check if we're on staging branch
    const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    log(`📍 Current branch: ${currentBranch}`, 'blue');
    
    if (currentBranch !== 'staging') {
      log('⚠️  Not on staging branch. Switching to staging...', 'yellow');
      runCommand('git checkout staging', 'Switch to staging branch');
    }
    
    // Pull latest changes
    runCommand('git pull origin staging', 'Pull latest staging changes');
    
    // Build the project for staging
    log('\n🏗️  Building project for staging...', 'cyan');
    runCommand('npm run build:staging', 'Build staging version');
    
    // Check if dist folder exists
    if (!fs.existsSync('dist')) {
      throw new Error('Build failed - dist folder not found');
    }
    
    log('✅ Build completed successfully', 'green');
    log(`📁 Build output in: ${path.resolve('dist')}`, 'blue');
    
    // List build files
    const distFiles = fs.readdirSync('dist');
    log('\n📋 Build files:', 'cyan');
    distFiles.forEach(file => {
      const filePath = path.join('dist', file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024).toFixed(2);
      log(`   ${file} (${size} KB)`, 'blue');
    });
    
    log('\n🎯 Next Steps:', 'cyan');
    log('1. Check Netlify dashboard for auto-deploy', 'yellow');
    log('2. If no auto-deploy, manually drag & drop the dist folder to Netlify', 'yellow');
    log('3. Or use Netlify CLI: netlify deploy --prod --dir=dist', 'yellow');
    
    // Check if Netlify CLI is available
    try {
      execSync('netlify --version', { stdio: 'pipe' });
      log('\n🔧 Netlify CLI detected. You can run:', 'green');
      log('   netlify deploy --prod --dir=dist', 'cyan');
    } catch {
      log('\n💡 Install Netlify CLI for easier deployment:', 'yellow');
      log('   npm install -g netlify-cli', 'cyan');
    }
    
    log('\n🌐 Staging URL: https://gudang-mitra-staging.netlify.app', 'green');
    
  } catch (error) {
    log(`\n❌ Deployment preparation failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
