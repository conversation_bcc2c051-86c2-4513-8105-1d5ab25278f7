#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🗄️  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

function main() {
  heading('Database Copy Setup Guide');
  
  log('\n📋 Opsi Copy Database:', 'blue');
  log('1. Copy dari database lokal (localhost)', 'blue');
  log('2. Copy dari Railway production database', 'blue');
  log('3. Copy dari database eksternal lainnya', 'blue');
  log('4. Import dari file SQL backup', 'blue');
  
  log('\n🔧 Langkah-langkah Setup:', 'yellow');
  
  log('\n1️⃣  Install dependencies (jika belum):', 'cyan');
  log('   npm install mysql2', 'blue');
  
  log('\n2️⃣  Setup source database config:', 'cyan');
  log('   cp database-copy-config.env.template database-copy-config.env', 'blue');
  log('   # Edit database-copy-config.env dengan detail database source Anda', 'blue');
  
  log('\n3️⃣  Pilih metode copy:', 'cyan');
  
  log('\n   📤 Export database source ke file:', 'yellow');
  log('   node copy-database.cjs export source backup.sql', 'blue');
  
  log('\n   📥 Import file SQL ke staging:', 'yellow');
  log('   node copy-database.cjs import staging backup.sql', 'blue');
  
  log('\n   🔄 Copy langsung (export + import):', 'yellow');
  log('   node copy-database.cjs copy source staging', 'blue');
  
  log('\n📊 Informasi Database Staging:', 'cyan');
  log('Host: nozomi.proxy.rlwy.net:14321', 'blue');
  log('Database: railway', 'blue');
  log('User: root', 'blue');
  
  log('\n⚠️  Peringatan:', 'red');
  log('- Copy database akan MENGGANTI semua data di staging', 'red');
  log('- Pastikan backup database staging jika diperlukan', 'red');
  log('- Test koneksi database source terlebih dahulu', 'red');
  
  log('\n🔍 Test koneksi database:', 'yellow');
  log('   node test-database-connection.cjs', 'blue');
  
  log('\n📝 Contoh konfigurasi database-copy-config.env:', 'cyan');
  log('', 'blue');
  log('# Untuk database lokal:', 'blue');
  log('SOURCE_DB_HOST=localhost', 'blue');
  log('SOURCE_DB_PORT=3306', 'blue');
  log('SOURCE_DB_USER=root', 'blue');
  log('SOURCE_DB_PASSWORD=your_password', 'blue');
  log('SOURCE_DB_NAME=gudang_mitra', 'blue');
  log('SOURCE_DB_SSL=false', 'blue');
  log('', 'blue');
  log('# Untuk Railway production:', 'blue');
  log('SOURCE_DB_HOST=your-production-host.railway.app', 'blue');
  log('SOURCE_DB_PORT=your-port', 'blue');
  log('SOURCE_DB_USER=root', 'blue');
  log('SOURCE_DB_PASSWORD=your-production-password', 'blue');
  log('SOURCE_DB_NAME=railway', 'blue');
  log('SOURCE_DB_SSL=true', 'blue');
  
  log('\n✅ Setup completed! Silakan ikuti langkah-langkah di atas.', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { main };
