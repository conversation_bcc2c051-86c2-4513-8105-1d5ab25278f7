#!/usr/bin/env node

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('📋 STEP 2: Railway Database Setup', 'cyan');
  log('=================================', 'cyan');

  log('\n🎯 GOAL: Connect staging backend to staging database', 'yellow');

  log('\n✅ Current Status:', 'green');
  log('- Railway project: ✅ gudangmitra-staging created', 'blue');
  log('- MySQL database: ✅ Created', 'blue');
  log('- Backend service: ✅ backend-staging deployed', 'blue');
  log('- Database connection: ❌ Need to configure', 'red');

  log('\n🌐 2.1 Open Railway Dashboard:', 'yellow');
  log('https://railway.com/project/************************************', 'blue');

  log('\n🗄️  2.2 Get MySQL Database Credentials:', 'yellow');
  log('1. Click on "MySQL" service (database icon)', 'blue');
  log('2. Click "Variables" tab', 'blue');
  log('3. Copy these values (write them down):', 'blue');
  log('', 'blue');
  log('   📝 MYSQL_HOST: ________________', 'blue');
  log('   📝 MYSQL_USER: ________________', 'blue');
  log('   📝 MYSQL_PASSWORD: ________________', 'blue');
  log('   📝 MYSQL_DATABASE: ________________', 'blue');
  log('   📝 MYSQL_PORT: 3306 (usually)', 'blue');

  log('\n⚙️  2.3 Set Variables in Backend Service:', 'yellow');
  log('1. Go back to project overview (click project name)', 'blue');
  log('2. Click on "backend-staging" service', 'blue');
  log('3. Click "Variables" tab', 'blue');
  log('4. Click "New Variable" button', 'blue');
  log('5. Add these variables ONE BY ONE:', 'blue');

  log('\n📝 Variables to Add:', 'cyan');
  log('===================', 'cyan');

  log('\n🔹 Variable 1:', 'yellow');
  log('   Name: DB_HOST', 'blue');
  log('   Value: [paste MYSQL_HOST value here]', 'blue');

  log('\n🔹 Variable 2:', 'yellow');
  log('   Name: DB_USER', 'blue');
  log('   Value: [paste MYSQL_USER value here]', 'blue');

  log('\n🔹 Variable 3:', 'yellow');
  log('   Name: DB_PASSWORD', 'blue');
  log('   Value: [paste MYSQL_PASSWORD value here]', 'blue');

  log('\n🔹 Variable 4:', 'yellow');
  log('   Name: DB_NAME', 'blue');
  log('   Value: [paste MYSQL_DATABASE value here]', 'blue');

  log('\n🔹 Variable 5:', 'yellow');
  log('   Name: DB_PORT', 'blue');
  log('   Value: 3306', 'blue');

  log('\n🔹 Variable 6:', 'yellow');
  log('   Name: DB_SSL', 'blue');
  log('   Value: true', 'blue');

  log('\n💡 EXAMPLE (Your values will be different):', 'cyan');
  log('============================================', 'cyan');

  log('\n📝 Example MySQL values:', 'yellow');
  log('   MYSQL_HOST: mysql.railway.internal', 'blue');
  log('   MYSQL_USER: root', 'blue');
  log('   MYSQL_PASSWORD: abc123xyz789randomstring', 'blue');
  log('   MYSQL_DATABASE: railway', 'blue');
  log('   MYSQL_PORT: 3306', 'blue');

  log('\n📝 So you would add these variables to backend-staging:', 'yellow');
  log('   DB_HOST = mysql.railway.internal', 'blue');
  log('   DB_USER = root', 'blue');
  log('   DB_PASSWORD = abc123xyz789randomstring', 'blue');
  log('   DB_NAME = railway', 'blue');
  log('   DB_PORT = 3306', 'blue');
  log('   DB_SSL = true', 'blue');

  log('\n🔄 2.4 Redeploy Backend Service:', 'yellow');
  log('1. After adding all variables', 'blue');
  log('2. In backend-staging service', 'blue');
  log('3. Click "Deployments" tab', 'blue');
  log('4. Click "Redeploy" on the latest deployment', 'blue');
  log('5. Wait for deployment to complete', 'blue');

  log('\n✅ 2.5 Verify Database Connection:', 'yellow');
  log('1. Wait for redeploy to finish', 'blue');
  log('2. In backend-staging service', 'blue');
  log('3. Click "Settings" tab', 'blue');
  log('4. Look for "Public Domain" or click "Generate Domain"', 'blue');
  log('5. Copy the URL (e.g., https://backend-staging-production-xxxx.up.railway.app)', 'blue');
  log('6. Test: Open [your-backend-url]/health in browser', 'blue');
  log('7. Test: Open [your-backend-url]/api/test-connection in browser', 'blue');

  log('\n🎯 SUCCESS CRITERIA:', 'green');
  log('====================', 'green');

  log('\n✅ Health endpoint should return:', 'yellow');
  log('   {"success": true, "message": "Railway server is running", ...}', 'blue');

  log('\n✅ Database test should return:', 'yellow');
  log('   {"success": true, "message": "Database connection successful"}', 'blue');

  log('\n🆘 TROUBLESHOOTING:', 'cyan');
  log('==================', 'cyan');

  log('\n❌ "Database connection failed" error:', 'red');
  log('   → Double-check all DB_* variables are set correctly', 'blue');
  log('   → Make sure you copied the exact values from MySQL service', 'blue');
  log('   → Verify MySQL service is running (green status)', 'blue');

  log('\n❌ "Service not accessible" error:', 'red');
  log('   → Make sure you generated a public domain', 'blue');
  log('   → Wait for deployment to complete', 'blue');
  log('   → Check deployment logs for errors', 'blue');

  log('\n❌ Variables not working:', 'red');
  log('   → Redeploy the service after adding variables', 'blue');
  log('   → Check variable names are exactly: DB_HOST, DB_USER, etc.', 'blue');

  log('\n📋 AFTER SUCCESSFUL DATABASE SETUP:', 'green');
  log('===================================', 'green');

  log('\n✅ You should have:', 'yellow');
  log('   - Backend URL accessible', 'blue');
  log('   - Health endpoint working', 'blue');
  log('   - Database connection working', 'blue');
  log('   - All environment variables set', 'blue');

  log('\n🚀 NEXT STEPS:', 'cyan');
  log('==============', 'cyan');

  log('\n📋 After database setup is complete:', 'yellow');
  log('1. ✅ Git repository connected', 'green');
  log('2. ✅ Railway database connected', 'green');
  log('3. 🔄 Get backend URL and update frontend config', 'blue');
  log('4. 🔄 Setup Netlify branch deploy', 'blue');
  log('5. 🔄 Configure CORS', 'blue');
  log('6. 🔄 Test everything', 'blue');

  log('\n📝 Continue to next step:', 'yellow');
  log('   node step3-backend-url.cjs', 'blue');
  log('   # OR follow COMPLETE-SETUP-CHECKLIST.md', 'blue');

  log('\n✅ Ready to configure database!', 'green');
  log('Open Railway dashboard and follow the steps above.', 'yellow');
}

if (require.main === module) {
  main();
}

module.exports = { main };
