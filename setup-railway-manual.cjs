#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('🚂 Railway Staging Setup Guide', 'cyan');
  log('==============================', 'cyan');
  
  log('\n📋 Manual Setup Steps:', 'yellow');
  log('Follow these steps to setup Railway staging:', 'yellow');
  
  log('\n1️⃣ Create New Railway Project:', 'blue');
  log('   - Go to: https://railway.app/dashboard', 'blue');
  log('   - Click "New Project"', 'blue');
  log('   - Choose "Empty Project"', 'blue');
  log('   - Name it: "gudangmitra-staging"', 'blue');
  
  log('\n2️⃣ Add MySQL Database:', 'blue');
  log('   - In your new project, click "Add Service"', 'blue');
  log('   - Choose "Database" → "MySQL"', 'blue');
  log('   - Wait for database to deploy', 'blue');
  
  log('\n3️⃣ Add Backend Service:', 'blue');
  log('   - Click "Add Service" again', 'blue');
  log('   - Choose "GitHub Repo" or "Empty Service"', 'blue');
  log('   - If using GitHub, connect your repository', 'blue');
  log('   - Name the service: "backend-staging"', 'blue');
  
  log('\n4️⃣ Configure Environment Variables:', 'blue');
  log('   - Click on your backend service', 'blue');
  log('   - Go to "Variables" tab', 'blue');
  log('   - Add these variables from your MySQL service:', 'blue');
  log('     * DB_HOST = [MySQL MYSQL_HOST]', 'blue');
  log('     * DB_USER = [MySQL MYSQL_USER]', 'blue');
  log('     * DB_PASSWORD = [MySQL MYSQL_PASSWORD]', 'blue');
  log('     * DB_NAME = [MySQL MYSQL_DATABASE]', 'blue');
  log('     * DB_PORT = [MySQL MYSQL_PORT]', 'blue');
  log('     * DB_SSL = true', 'blue');
  log('     * NODE_ENV = staging', 'blue');
  log('     * PORT = 3002', 'blue');
  log('     * CORS_ORIGIN = https://gudang-staging.netlify.app', 'blue');
  
  log('\n5️⃣ Deploy Backend:', 'blue');
  log('   - In backend service, go to "Settings"', 'blue');
  log('   - Set "Start Command": node server/railway-server.js', 'blue');
  log('   - Set "Root Directory": /', 'blue');
  log('   - Click "Deploy"', 'blue');
  
  log('\n6️⃣ Get Backend URL:', 'blue');
  log('   - After deployment, go to "Settings"', 'blue');
  log('   - Click "Generate Domain" or use the provided URL', 'blue');
  log('   - Copy the URL (e.g., https://backend-staging-production.up.railway.app)', 'blue');
  
  // Create local environment files
  log('\n📝 Creating local environment files...', 'cyan');
  
  // Create server/.env.staging template
  const serverEnvContent = `# Staging Environment Variables
# Update these values with actual Railway database credentials

DB_HOST=your-mysql-host-from-railway
DB_USER=your-mysql-user-from-railway
DB_PASSWORD=your-mysql-password-from-railway
DB_NAME=your-mysql-database-from-railway
DB_PORT=3306
DB_SSL=true

PORT=3002
NODE_ENV=staging

# CORS Configuration - Update with actual Netlify staging URL
CORS_ORIGIN=https://gudang-staging.netlify.app

# Security
JWT_SECRET=staging-jwt-secret-${Math.random().toString(36).substring(2, 15)}
`;

  fs.writeFileSync('server/.env.staging', serverEnvContent);
  log('✅ Created server/.env.staging template', 'green');
  
  // Create frontend .env.staging template
  const frontendEnvContent = `# Staging Environment Variables
# Update with actual Railway backend URL

VITE_API_URL=https://your-backend-staging-url.up.railway.app/api
VITE_APP_ENV=staging
`;

  fs.writeFileSync('.env.staging', frontendEnvContent);
  log('✅ Created .env.staging template', 'green');
  
  log('\n7️⃣ Update Local Environment Files:', 'yellow');
  log('   - Edit server/.env.staging with actual database credentials', 'yellow');
  log('   - Edit .env.staging with actual backend URL', 'yellow');
  
  log('\n8️⃣ Test Connection (Optional):', 'blue');
  log('   - Run: node test-railway-connection.cjs', 'blue');
  
  // Create a simple connection test script
  const testScript = `#!/usr/bin/env node

const mysql = require('mysql2/promise');
require('dotenv').config({ path: 'server/.env.staging' });

async function testConnection() {
  try {
    console.log('🔍 Testing Railway database connection...');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT,
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
    });
    
    await connection.execute('SELECT 1 as test');
    await connection.end();
    
    console.log('✅ Database connection successful!');
    console.log('🎉 Railway staging database is ready!');
  } catch (error) {
    console.log('❌ Database connection failed:');
    console.log(error.message);
    console.log('\\n💡 Please check your environment variables in server/.env.staging');
  }
}

testConnection();
`;

  fs.writeFileSync('test-railway-connection.cjs', testScript);
  log('✅ Created test-railway-connection.cjs', 'green');
  
  log('\n🔗 Useful Links:', 'cyan');
  log('- Railway Dashboard: https://railway.app/dashboard', 'blue');
  log('- Railway Docs: https://docs.railway.app/', 'blue');
  log('- MySQL Variables: Check your MySQL service Variables tab', 'blue');
  
  log('\n📋 Next Steps After Railway Setup:', 'cyan');
  log('1. Complete Railway setup following steps above', 'yellow');
  log('2. Update local .env files with actual credentials', 'yellow');
  log('3. Test connection: node test-railway-connection.cjs', 'yellow');
  log('4. Setup Netlify staging: node setup-netlify-staging-complete.cjs', 'yellow');
  log('5. Test full environment: node test-staging-environment.cjs', 'yellow');
  
  log('\n✨ Files Created:', 'green');
  log('- server/.env.staging (template)', 'blue');
  log('- .env.staging (template)', 'blue');
  log('- test-railway-connection.cjs', 'blue');
}

if (require.main === module) {
  main();
}

module.exports = { main };
