#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const https = require('https');
const http = require('http');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Make HTTP request
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    const timeout = options.timeout || 10000;
    
    const req = protocol.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Test backend API endpoints
async function testBackendAPI(backendUrl) {
  log('\n🔍 Testing Backend API...', 'cyan');
  
  const endpoints = [
    { path: '/', name: 'Health Check' },
    { path: '/api', name: 'API Root' },
    { path: '/api/health', name: 'API Health' },
    { path: '/api/users', name: 'Users Endpoint' },
    { path: '/api/items', name: 'Items Endpoint' },
    { path: '/api/requests', name: 'Requests Endpoint' }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      log(`Testing ${endpoint.name}: ${backendUrl}${endpoint.path}`, 'blue');
      const response = await makeRequest(`${backendUrl}${endpoint.path}`);
      
      if (response.statusCode >= 200 && response.statusCode < 400) {
        log(`✅ ${endpoint.name}: ${response.statusCode}`, 'green');
        results.push({ ...endpoint, status: 'success', code: response.statusCode });
      } else {
        log(`⚠️  ${endpoint.name}: ${response.statusCode}`, 'yellow');
        results.push({ ...endpoint, status: 'warning', code: response.statusCode });
      }
    } catch (error) {
      log(`❌ ${endpoint.name}: ${error.message}`, 'red');
      results.push({ ...endpoint, status: 'error', error: error.message });
    }
  }
  
  return results;
}

// Test frontend site
async function testFrontendSite(frontendUrl) {
  log('\n🔍 Testing Frontend Site...', 'cyan');
  
  try {
    log(`Testing frontend: ${frontendUrl}`, 'blue');
    const response = await makeRequest(frontendUrl);
    
    if (response.statusCode === 200) {
      log('✅ Frontend site is accessible', 'green');
      
      // Check if it's a React app
      if (response.data.includes('react') || response.data.includes('vite') || response.data.includes('root')) {
        log('✅ React application detected', 'green');
      }
      
      // Check for staging indicators
      if (response.data.includes('staging') || response.data.includes('Staging')) {
        log('✅ Staging environment detected', 'green');
      }
      
      return { status: 'success', code: response.statusCode };
    } else {
      log(`⚠️  Frontend returned: ${response.statusCode}`, 'yellow');
      return { status: 'warning', code: response.statusCode };
    }
  } catch (error) {
    log(`❌ Frontend test failed: ${error.message}`, 'red');
    return { status: 'error', error: error.message };
  }
}

// Test database connection
async function testDatabaseConnection() {
  log('\n🔍 Testing Database Connection...', 'cyan');
  
  try {
    // Check if .env.staging exists
    const envPath = './server/.env.staging';
    if (!fs.existsSync(envPath)) {
      log('❌ server/.env.staging not found', 'red');
      return { status: 'error', error: 'Environment file not found' };
    }
    
    // Read database config
    const envContent = fs.readFileSync(envPath, 'utf8');
    const dbHost = envContent.match(/DB_HOST=(.+)/)?.[1];
    const dbName = envContent.match(/DB_NAME=(.+)/)?.[1];
    
    if (!dbHost || !dbName) {
      log('❌ Database configuration incomplete', 'red');
      return { status: 'error', error: 'Database config incomplete' };
    }
    
    log(`Database Host: ${dbHost}`, 'blue');
    log(`Database Name: ${dbName}`, 'blue');
    
    // Try to test connection using a simple script
    try {
      const testScript = `
        require('dotenv').config({ path: '.env.staging' });
        const mysql = require('mysql2/promise');
        
        async function testConnection() {
          try {
            const connection = await mysql.createConnection({
              host: process.env.DB_HOST,
              user: process.env.DB_USER,
              password: process.env.DB_PASSWORD,
              database: process.env.DB_NAME,
              port: process.env.DB_PORT,
              ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
            });
            
            await connection.execute('SELECT 1');
            await connection.end();
            console.log('SUCCESS');
          } catch (error) {
            console.log('ERROR:', error.message);
          }
        }
        
        testConnection();
      `;
      
      fs.writeFileSync('./temp-db-test.js', testScript);
      const result = execSync('cd server && node ../temp-db-test.js', { encoding: 'utf8', cwd: process.cwd() });
      fs.unlinkSync('./temp-db-test.js');
      
      if (result.includes('SUCCESS')) {
        log('✅ Database connection successful', 'green');
        return { status: 'success' };
      } else {
        log('❌ Database connection failed', 'red');
        return { status: 'error', error: result };
      }
    } catch (error) {
      log('❌ Database test failed', 'red');
      return { status: 'error', error: error.message };
    }
  } catch (error) {
    log('❌ Database test setup failed', 'red');
    return { status: 'error', error: error.message };
  }
}

// Get URLs from configuration
function getConfiguredURLs() {
  const urls = {};
  
  try {
    // Get backend URL from frontend .env.staging
    const frontendEnv = fs.readFileSync('.env.staging', 'utf8');
    const apiUrl = frontendEnv.match(/VITE_API_URL=(.+)/)?.[1];
    if (apiUrl) {
      urls.backend = apiUrl.replace('/api', '');
      urls.api = apiUrl;
    }
  } catch (error) {
    log('⚠️  Could not read frontend .env.staging', 'yellow');
  }
  
  try {
    // Get frontend URL from netlify status or guess
    try {
      const netlifyStatus = execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
      const urlMatch = netlifyStatus.match(/URL:\s*(https?:\/\/[^\s]+)/);
      if (urlMatch) {
        urls.frontend = urlMatch[1];
      }
    } catch (error) {
      // Fallback to common staging URL pattern
      urls.frontend = 'https://gudang-staging.netlify.app';
    }
  } catch (error) {
    log('⚠️  Could not determine frontend URL', 'yellow');
  }
  
  return urls;
}

// Main testing function
async function main() {
  log('🧪 Staging Environment Testing', 'cyan');
  log('==============================', 'cyan');
  
  // Get configured URLs
  const urls = getConfiguredURLs();
  
  log('\n📋 Configuration:', 'cyan');
  log(`Frontend URL: ${urls.frontend || 'Not configured'}`, 'blue');
  log(`Backend URL: ${urls.backend || 'Not configured'}`, 'blue');
  log(`API URL: ${urls.api || 'Not configured'}`, 'blue');
  
  const testResults = {
    database: null,
    backend: null,
    frontend: null
  };
  
  // Test 1: Database Connection
  testResults.database = await testDatabaseConnection();
  
  // Test 2: Backend API
  if (urls.backend) {
    testResults.backend = await testBackendAPI(urls.backend);
  } else {
    log('\n❌ Backend URL not configured, skipping API tests', 'red');
  }
  
  // Test 3: Frontend Site
  if (urls.frontend) {
    testResults.frontend = await testFrontendSite(urls.frontend);
  } else {
    log('\n❌ Frontend URL not configured, skipping frontend tests', 'red');
  }
  
  // Summary
  log('\n📊 Test Results Summary', 'cyan');
  log('=======================', 'cyan');
  
  // Database
  if (testResults.database?.status === 'success') {
    log('✅ Database: Connected', 'green');
  } else {
    log('❌ Database: Failed', 'red');
  }
  
  // Backend
  if (testResults.backend && Array.isArray(testResults.backend)) {
    const successCount = testResults.backend.filter(r => r.status === 'success').length;
    const totalCount = testResults.backend.length;
    log(`📡 Backend: ${successCount}/${totalCount} endpoints working`, successCount === totalCount ? 'green' : 'yellow');
  } else {
    log('❌ Backend: Not tested', 'red');
  }
  
  // Frontend
  if (testResults.frontend?.status === 'success') {
    log('✅ Frontend: Accessible', 'green');
  } else {
    log('❌ Frontend: Failed', 'red');
  }
  
  // Overall status
  const allPassed = testResults.database?.status === 'success' && 
                   testResults.frontend?.status === 'success' &&
                   testResults.backend?.every(r => r.status === 'success');
  
  if (allPassed) {
    log('\n🎉 All tests passed! Staging environment is ready!', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please check the issues above.', 'yellow');
  }
  
  // Next steps
  log('\n📋 Next Steps:', 'cyan');
  if (!allPassed) {
    log('1. Fix any failing tests above', 'yellow');
    log('2. Redeploy if necessary', 'yellow');
    log('3. Run this test again', 'yellow');
  } else {
    log('1. Start testing your application features', 'green');
    log('2. Deploy updates to staging before production', 'green');
    log('3. Use staging for QA and testing', 'green');
  }
  
  log(`\n🔗 Access your staging environment:`, 'blue');
  if (urls.frontend) log(`   Frontend: ${urls.frontend}`, 'blue');
  if (urls.api) log(`   API: ${urls.api}`, 'blue');
}

// Run the tests
if (require.main === module) {
  main().catch(error => {
    log('❌ Testing failed: ' + error.message, 'red');
    process.exit(1);
  });
}

module.exports = { main };
