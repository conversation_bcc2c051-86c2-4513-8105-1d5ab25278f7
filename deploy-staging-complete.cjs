#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description, options = {}) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ Error during ${description}:`, 'red');
    log(error.message, 'red');
    return null;
  }
}

// Check prerequisites
function checkPrerequisites() {
  log('🔍 Checking Prerequisites...', 'cyan');
  
  const checks = [
    { command: 'node --version', name: 'Node.js' },
    { command: 'npm --version', name: 'NPM' },
    { command: 'git --version', name: 'Git' }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const version = execSync(check.command, { encoding: 'utf8', stdio: 'pipe' }).trim();
      log(`✅ ${check.name}: ${version}`, 'green');
    } catch (error) {
      log(`❌ ${check.name}: Not installed`, 'red');
      allPassed = false;
    }
  }
  
  // Check optional tools
  const optionalChecks = [
    { command: 'railway --version', name: 'Railway CLI', install: 'npm install -g @railway/cli' },
    { command: 'netlify --version', name: 'Netlify CLI', install: 'npm install -g netlify-cli' }
  ];
  
  for (const check of optionalChecks) {
    try {
      const version = execSync(check.command, { encoding: 'utf8', stdio: 'pipe' }).trim();
      log(`✅ ${check.name}: ${version}`, 'green');
    } catch (error) {
      log(`⚠️  ${check.name}: Not installed`, 'yellow');
      log(`   Install with: ${check.install}`, 'blue');
    }
  }
  
  return allPassed;
}

// Install dependencies
function installDependencies() {
  log('\n📦 Installing Dependencies...', 'cyan');
  
  try {
    // Install root dependencies
    executeCommand('npm install', 'Installing frontend dependencies');
    
    // Install server dependencies
    executeCommand('cd server && npm install', 'Installing backend dependencies');
    
    log('✅ All dependencies installed', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to install dependencies', 'red');
    return false;
  }
}

// Run Railway setup
function runRailwaySetup() {
  log('\n🚂 Setting up Railway Staging...', 'cyan');
  
  try {
    if (fs.existsSync('./setup-railway-staging-complete.cjs')) {
      executeCommand('node setup-railway-staging-complete.cjs', 'Running Railway setup');
      return true;
    } else {
      log('❌ Railway setup script not found', 'red');
      return false;
    }
  } catch (error) {
    log('❌ Railway setup failed', 'red');
    log('Please run Railway setup manually', 'yellow');
    return false;
  }
}

// Run Netlify setup
function runNetlifySetup() {
  log('\n🌐 Setting up Netlify Staging...', 'cyan');
  
  try {
    if (fs.existsSync('./setup-netlify-staging-complete.cjs')) {
      executeCommand('node setup-netlify-staging-complete.cjs', 'Running Netlify setup');
      return true;
    } else {
      log('❌ Netlify setup script not found', 'red');
      return false;
    }
  } catch (error) {
    log('❌ Netlify setup failed', 'red');
    log('Please run Netlify setup manually', 'yellow');
    return false;
  }
}

// Run environment tests
function runTests() {
  log('\n🧪 Testing Staging Environment...', 'cyan');
  
  try {
    if (fs.existsSync('./test-staging-environment.cjs')) {
      executeCommand('node test-staging-environment.cjs', 'Running environment tests');
      return true;
    } else {
      log('❌ Test script not found', 'red');
      return false;
    }
  } catch (error) {
    log('❌ Tests failed', 'red');
    log('Please check the staging environment manually', 'yellow');
    return false;
  }
}

// Show manual setup instructions
function showManualInstructions() {
  log('\n📋 Manual Setup Instructions', 'cyan');
  log('============================', 'cyan');
  
  log('\n🚂 Railway Setup:', 'yellow');
  log('1. Install Railway CLI: npm install -g @railway/cli', 'blue');
  log('2. Login to Railway: railway login', 'blue');
  log('3. Run: node setup-railway-staging-complete.cjs', 'blue');
  
  log('\n🌐 Netlify Setup:', 'yellow');
  log('1. Install Netlify CLI: npm install -g netlify-cli', 'blue');
  log('2. Login to Netlify: netlify login', 'blue');
  log('3. Run: node setup-netlify-staging-complete.cjs', 'blue');
  
  log('\n🧪 Testing:', 'yellow');
  log('1. Run: node test-staging-environment.cjs', 'blue');
  
  log('\n📁 Files Created:', 'yellow');
  log('- setup-railway-staging-complete.cjs', 'blue');
  log('- setup-netlify-staging-complete.cjs', 'blue');
  log('- test-staging-environment.cjs', 'blue');
  log('- deploy-staging-complete.cjs (this file)', 'blue');
}

// Create summary report
function createSummaryReport() {
  log('\n📊 Creating Summary Report...', 'cyan');
  
  const report = `# Staging Deployment Summary

Generated on: ${new Date().toISOString()}

## Scripts Created

1. **setup-railway-staging-complete.cjs**
   - Creates Railway staging project
   - Adds MySQL database
   - Updates environment variables
   - Deploys backend

2. **setup-netlify-staging-complete.cjs**
   - Creates staging branch
   - Updates Netlify configuration
   - Builds and deploys frontend
   - Updates CORS settings

3. **test-staging-environment.cjs**
   - Tests database connection
   - Tests backend API endpoints
   - Tests frontend accessibility
   - Provides comprehensive report

4. **deploy-staging-complete.cjs**
   - Master script to run all setup
   - Checks prerequisites
   - Installs dependencies
   - Coordinates deployment

## Environment Files

- \`.env.staging\` - Frontend environment variables
- \`server/.env.staging\` - Backend environment variables
- \`netlify.toml\` - Netlify deployment configuration
- \`railway-staging.toml\` - Railway deployment configuration

## URLs (will be updated after deployment)

- Frontend Staging: https://gudang-staging.netlify.app
- Backend Staging: https://gudangmitra-staging.up.railway.app
- API Staging: https://gudangmitra-staging.up.railway.app/api

## Next Steps

1. Install CLI tools (Railway, Netlify)
2. Login to both services
3. Run the setup scripts
4. Test the staging environment
5. Start using staging for development

## Support

If you encounter issues:
1. Check the logs in each script
2. Verify CLI tools are installed and authenticated
3. Check network connectivity
4. Review Railway and Netlify dashboards
`;

  fs.writeFileSync('STAGING-DEPLOYMENT-SUMMARY.md', report);
  log('✅ Summary report created: STAGING-DEPLOYMENT-SUMMARY.md', 'green');
}

// Main function
async function main() {
  log('🚀 Complete Staging Deployment Setup', 'magenta');
  log('====================================', 'magenta');
  
  // Step 1: Check prerequisites
  const prereqsPassed = checkPrerequisites();
  
  if (!prereqsPassed) {
    log('\n❌ Prerequisites check failed', 'red');
    log('Please install missing tools and try again', 'yellow');
    process.exit(1);
  }
  
  // Step 2: Install dependencies
  if (!installDependencies()) {
    log('\n❌ Dependency installation failed', 'red');
    process.exit(1);
  }
  
  // Step 3: Check if CLI tools are available and authenticated
  let canRunAutomated = true;
  
  try {
    execSync('railway whoami', { stdio: 'pipe' });
    log('✅ Railway CLI authenticated', 'green');
  } catch (error) {
    log('⚠️  Railway CLI not authenticated', 'yellow');
    canRunAutomated = false;
  }
  
  try {
    const netlifyStatus = execSync('netlify status', { encoding: 'utf8', stdio: 'pipe' });
    if (!netlifyStatus.includes('Not logged in')) {
      log('✅ Netlify CLI authenticated', 'green');
    } else {
      throw new Error('Not logged in');
    }
  } catch (error) {
    log('⚠️  Netlify CLI not authenticated', 'yellow');
    canRunAutomated = false;
  }
  
  if (canRunAutomated) {
    log('\n🤖 Running Automated Setup...', 'cyan');
    
    // Step 4: Run Railway setup
    const railwaySuccess = runRailwaySetup();
    
    // Step 5: Run Netlify setup
    const netlifySuccess = runNetlifySetup();
    
    // Step 6: Run tests
    if (railwaySuccess && netlifySuccess) {
      runTests();
    }
    
    if (railwaySuccess && netlifySuccess) {
      log('\n🎉 Automated setup completed successfully!', 'green');
    } else {
      log('\n⚠️  Automated setup had issues. Check manual instructions below.', 'yellow');
    }
  } else {
    log('\n📋 CLI tools need authentication. See manual instructions below.', 'yellow');
  }
  
  // Step 7: Show manual instructions
  showManualInstructions();
  
  // Step 8: Create summary report
  createSummaryReport();
  
  log('\n🎯 Setup Complete!', 'magenta');
  log('==================', 'magenta');
  log('All scripts and configurations have been created.', 'green');
  log('Follow the manual instructions above to complete the deployment.', 'green');
  log('Check STAGING-DEPLOYMENT-SUMMARY.md for detailed information.', 'blue');
}

// Run the setup
if (require.main === module) {
  main().catch(error => {
    log('❌ Setup failed: ' + error.message, 'red');
    process.exit(1);
  });
}

module.exports = { main };
