-- Sample Data for Gudang Mitra System
-- Generated for staging environment testing

SET FOREIGN_KEY_CHECKS = 0;

-- Table: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','manager','staff') NOT NULL DEFAULT 'staff',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Data for table: users
INSERT INTO `users` (`name`, `email`, `password`, `role`) VALUES 
('Admin User', '<EMAIL>', '$2b$10$hash_for_password123', 'admin'),
('Manager User', '<EMAIL>', '$2b$10$hash_for_password123', 'manager'),
('Staff User 1', '<EMAIL>', '$2b$10$hash_for_password123', 'staff'),
('Staff User 2', '<EMAIL>', '$2b$10$hash_for_password123', 'staff');

-- Table: categories
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Data for table: categories
INSERT INTO `categories` (`name`, `description`) VALUES 
('Electronics', 'Electronic devices and components'),
('Office Supplies', 'Office equipment and stationery'),
('Furniture', 'Office and warehouse furniture'),
('Tools', 'Hand tools and equipment'),
('Safety Equipment', 'Safety gear and protective equipment');

-- Table: items
DROP TABLE IF EXISTS `items`;
CREATE TABLE `items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `category_id` int,
  `sku` varchar(100) UNIQUE,
  `quantity` int NOT NULL DEFAULT 0,
  `min_quantity` int NOT NULL DEFAULT 10,
  `unit_price` decimal(10,2) DEFAULT 0.00,
  `location` varchar(255),
  `status` enum('active','inactive','discontinued') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_items_category` (`category_id`),
  CONSTRAINT `fk_items_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Data for table: items
INSERT INTO `items` (`name`, `description`, `category_id`, `sku`, `quantity`, `min_quantity`, `unit_price`, `location`) VALUES 
('Laptop Dell Inspiron', 'Dell Inspiron 15 3000 Series', 1, 'ELEC-001', 25, 5, 8500000.00, 'A1-01'),
('Wireless Mouse', 'Logitech M705 Wireless Mouse', 1, 'ELEC-002', 50, 10, 350000.00, 'A1-02'),
('Office Chair', 'Ergonomic office chair with lumbar support', 3, 'FURN-001', 15, 3, 1200000.00, 'B2-01'),
('A4 Paper', 'White A4 copy paper 80gsm', 2, 'OFFC-001', 200, 50, 45000.00, 'C1-01'),
('Safety Helmet', 'Industrial safety helmet', 5, 'SAFE-001', 30, 10, 125000.00, 'D1-01'),
('Screwdriver Set', 'Professional screwdriver set 12pcs', 4, 'TOOL-001', 20, 5, 275000.00, 'E1-01'),
('Printer Canon', 'Canon PIXMA G2010 All-in-One', 1, 'ELEC-003', 8, 2, 1850000.00, 'A1-03'),
('Desk Lamp', 'LED desk lamp adjustable', 1, 'ELEC-004', 35, 8, 185000.00, 'A2-01'),
('Filing Cabinet', '4-drawer steel filing cabinet', 3, 'FURN-002', 12, 3, 950000.00, 'B2-02'),
('Stapler', 'Heavy duty stapler', 2, 'OFFC-002', 45, 10, 85000.00, 'C1-02');

-- Table: requests
DROP TABLE IF EXISTS `requests`;
CREATE TABLE `requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `requester_name` varchar(255) NOT NULL,
  `requester_email` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `request_date` date NOT NULL,
  `needed_date` date NOT NULL,
  `status` enum('pending','approved','rejected','completed') NOT NULL DEFAULT 'pending',
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `notes` text,
  `approved_by` int NULL,
  `approved_at` timestamp NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_requests_approved_by` (`approved_by`),
  CONSTRAINT `fk_requests_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Data for table: requests
INSERT INTO `requests` (`requester_name`, `requester_email`, `department`, `request_date`, `needed_date`, `status`, `priority`, `notes`) VALUES 
('John Doe', '<EMAIL>', 'IT Department', '2025-01-10', '2025-01-15', 'pending', 'high', 'Need for new employee setup'),
('Jane Smith', '<EMAIL>', 'HR Department', '2025-01-11', '2025-01-20', 'approved', 'medium', 'Office renovation project'),
('Bob Wilson', '<EMAIL>', 'Operations', '2025-01-12', '2025-01-18', 'pending', 'urgent', 'Equipment replacement'),
('Alice Brown', '<EMAIL>', 'Finance', '2025-01-13', '2025-01-25', 'completed', 'low', 'Regular office supplies'),
('Charlie Davis', '<EMAIL>', 'Marketing', '2025-01-14', '2025-01-22', 'pending', 'medium', 'Campaign preparation');

-- Table: request_items
DROP TABLE IF EXISTS `request_items`;
CREATE TABLE `request_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `request_id` int NOT NULL,
  `item_id` int NOT NULL,
  `quantity_requested` int NOT NULL,
  `quantity_approved` int NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_request_items_request` (`request_id`),
  KEY `fk_request_items_item` (`item_id`),
  CONSTRAINT `fk_request_items_request` FOREIGN KEY (`request_id`) REFERENCES `requests` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_request_items_item` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Data for table: request_items
INSERT INTO `request_items` (`request_id`, `item_id`, `quantity_requested`, `quantity_approved`, `notes`) VALUES 
(1, 1, 2, NULL, 'For new developers'),
(1, 2, 2, NULL, 'Wireless mice for laptops'),
(2, 3, 5, 5, 'Approved for HR office'),
(2, 4, 10, 8, 'Reduced quantity due to budget'),
(3, 5, 15, NULL, 'Safety equipment for warehouse'),
(3, 6, 3, NULL, 'Maintenance tools'),
(4, 4, 20, 20, 'Monthly office supplies'),
(4, 10, 5, 5, 'Office equipment'),
(5, 7, 1, NULL, 'For marketing presentations'),
(5, 8, 3, NULL, 'Desk lighting for late work');

-- Table: inventory_transactions
DROP TABLE IF EXISTS `inventory_transactions`;
CREATE TABLE `inventory_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_id` int NOT NULL,
  `transaction_type` enum('in','out','adjustment') NOT NULL,
  `quantity` int NOT NULL,
  `reference_type` enum('purchase','request','adjustment','return') NOT NULL,
  `reference_id` int NULL,
  `notes` text,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_inventory_item` (`item_id`),
  KEY `fk_inventory_user` (`created_by`),
  CONSTRAINT `fk_inventory_item` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`),
  CONSTRAINT `fk_inventory_user` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Data for table: inventory_transactions
INSERT INTO `inventory_transactions` (`item_id`, `transaction_type`, `quantity`, `reference_type`, `reference_id`, `notes`, `created_by`) VALUES 
(1, 'in', 30, 'purchase', NULL, 'Initial stock purchase', 1),
(2, 'in', 60, 'purchase', NULL, 'Initial stock purchase', 1),
(3, 'in', 20, 'purchase', NULL, 'Initial stock purchase', 1),
(4, 'in', 250, 'purchase', NULL, 'Bulk paper purchase', 1),
(5, 'in', 40, 'purchase', NULL, 'Safety equipment stock', 1),
(1, 'out', 5, 'request', 1, 'Laptop allocation for IT dept', 2),
(2, 'out', 10, 'request', 1, 'Mouse allocation', 2),
(4, 'out', 50, 'request', 4, 'Monthly office supplies', 2);

SET FOREIGN_KEY_CHECKS = 1;
