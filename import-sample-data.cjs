#!/usr/bin/env node

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function heading(text) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`📊  ${text}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

async function main() {
  heading('Import Sample Data to Staging Database');
  
  log('\n📋 Sample data includes:', 'blue');
  log('- 4 Users (admin, manager, 2 staff)', 'blue');
  log('- 5 Categories (Electronics, Office Supplies, etc.)', 'blue');
  log('- 10 Items with stock quantities', 'blue');
  log('- 5 Sample requests with different statuses', 'blue');
  log('- Request items and inventory transactions', 'blue');
  
  log('\n⚠️  Warning:', 'red');
  log('This will REPLACE all data in staging database!', 'red');
  
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const answer = await new Promise(resolve => {
    readline.question('\nDo you want to continue? (y/N): ', resolve);
  });
  
  readline.close();
  
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    log('❌ Import cancelled.', 'yellow');
    return;
  }
  
  try {
    log('\n🚀 Starting import...', 'cyan');
    
    // Import sample data
    execSync('node copy-database.cjs import staging sample-data.sql', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    log('\n✅ Sample data imported successfully!', 'green');
    
    log('\n🔐 Test Login Credentials:', 'cyan');
    log('Email: <EMAIL>', 'blue');
    log('Password: password123', 'blue');
    log('Role: manager', 'blue');
    
    log('\n📊 Database now contains:', 'cyan');
    log('- Complete user management system', 'blue');
    log('- Item inventory with categories', 'blue');
    log('- Sample requests for testing', 'blue');
    log('- Transaction history', 'blue');
    
    log('\n🌐 Test the staging environment:', 'green');
    log('Frontend: https://gudang-mitra-staging.netlify.app', 'blue');
    log('Backend: https://backend-staging-production.up.railway.app', 'blue');
    
  } catch (error) {
    log(`\n❌ Import failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
